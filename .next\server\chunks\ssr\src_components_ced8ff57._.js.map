{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/about-hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { MapPin, Calendar, Users, Award } from 'lucide-react';\nimport { useCompanyInfo } from '@/lib/queries/hooks';\n\nexport default function AboutHero() {\n  const { data: company } = useCompanyInfo();\n\n  return (\n    <section className=\"relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\"\n          >\n            <Award className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            About Lunar Cubes\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\"\n          >\n            Transforming Nepal&apos;s Digital\n            <span className=\"block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent\">\n              Marketing Landscape\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            We are Nepal&apos;s premier digital marketing agency, dedicated to empowering small and \n            medium businesses with innovative strategies that drive growth and create lasting success \n            in the digital age.\n          </motion.p>\n\n          {/* Company Stats */}\n          {company && (\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\"\n            >\n              <div className=\"text-center\">\n                <div className=\"flex justify-center mb-3\">\n                  <Calendar className=\"h-8 w-8 text-brand-gold\" />\n                </div>\n                <div className=\"text-3xl font-bold text-brand-navy mb-2\">\n                  {company.founded}\n                </div>\n                <div className=\"text-gray-600\">Founded</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"flex justify-center mb-3\">\n                  <Users className=\"h-8 w-8 text-brand-gold\" />\n                </div>\n                <div className=\"text-3xl font-bold text-brand-navy mb-2\">\n                  {company.stats.clientsServed}+\n                </div>\n                <div className=\"text-gray-600\">Happy Clients</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"flex justify-center mb-3\">\n                  <Award className=\"h-8 w-8 text-brand-gold\" />\n                </div>\n                <div className=\"text-3xl font-bold text-brand-navy mb-2\">\n                  {company.stats.projectsCompleted}+\n                </div>\n                <div className=\"text-gray-600\">Projects</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"flex justify-center mb-3\">\n                  <MapPin className=\"h-8 w-8 text-brand-gold\" />\n                </div>\n                <div className=\"text-3xl font-bold text-brand-navy mb-2\">\n                  {company.stats.teamMembers}+\n                </div>\n                <div className=\"text-gray-600\">Team Members</div>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Mission Statement */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100\"\n          >\n            <h2 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-6\">\n              Our Mission\n            </h2>\n            {company && (\n              <p className=\"text-lg text-gray-700 leading-relaxed\">\n                {company.mission}\n              </p>\n            )}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEvC,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAKpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;8CAEC,8OAAC;oCAAK,WAAU;8CAAqF;;;;;;;;;;;;sCAMvG,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;wBAOA,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,OAAO;;;;;;sDAElB,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,KAAK,CAAC,aAAa;gDAAC;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,KAAK,CAAC,iBAAiB;gDAAC;;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,KAAK,CAAC,WAAW;gDAAC;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;gCAGjE,yBACC,8OAAC;oCAAE,WAAU;8CACV,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/about-story.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Lightbulb, Target, Rocket, Heart } from 'lucide-react';\n\nconst storyMilestones = [\n  {\n    year: '2019',\n    title: 'The Beginning',\n    description: 'Founded with a vision to bridge the digital gap for Nepali businesses. Started with a small team of passionate digital marketing enthusiasts.',\n    icon: Lightbulb,\n  },\n  {\n    year: '2020',\n    title: 'First Major Success',\n    description: 'Helped our first 50 clients achieve significant digital growth, establishing our reputation in the Kathmandu valley.',\n    icon: Target,\n  },\n  {\n    year: '2021',\n    title: 'Expansion & Growth',\n    description: 'Expanded our services and team, reaching clients across Nepal and achieving 200+ successful projects.',\n    icon: Rocket,\n  },\n  {\n    year: '2024',\n    title: 'Leading the Market',\n    description: 'Now serving 150+ happy clients with a team of 12+ experts, recognized as Nepal\\'s premier digital marketing agency.',\n    icon: Heart,\n  },\n];\n\nexport default function AboutStory() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n              Our Journey\n            </div>\n            \n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              From Humble Beginnings to\n              <span className=\"block text-brand-navy\">Digital Excellence</span>\n            </h2>\n            \n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              Lunar Cubes was born from a simple observation: Nepali businesses had incredible \n              potential but lacked the digital marketing expertise to reach their full potential \n              in the online world.\n            </p>\n\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-brand-navy rounded-xl flex items-center justify-center flex-shrink-0\">\n                  <Lightbulb className=\"h-6 w-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">The Vision</h3>\n                  <p className=\"text-gray-600\">\n                    To create a digital marketing agency that truly understands the Nepali market, \n                    culture, and business challenges while delivering world-class results.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-brand-gold rounded-xl flex items-center justify-center flex-shrink-0\">\n                  <Target className=\"h-6 w-6 text-gray-900\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">The Approach</h3>\n                  <p className=\"text-gray-600\">\n                    Combining global digital marketing best practices with deep local market \n                    insights to create strategies that actually work for Nepali businesses.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-gold rounded-xl flex items-center justify-center flex-shrink-0\">\n                  <Heart className=\"h-6 w-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">The Impact</h3>\n                  <p className=\"text-gray-600\">\n                    Today, we&apos;ve helped transform hundreds of Nepali businesses, creating jobs, \n                    driving economic growth, and putting Nepal on the digital map.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Right Content - Timeline */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            {/* Timeline Line */}\n            <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-brand-navy to-brand-gold\"></div>\n\n            <div className=\"space-y-12\">\n              {storyMilestones.map((milestone, index) => (\n                <motion.div\n                  key={milestone.year}\n                  initial={{ opacity: 0, x: 20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"relative flex items-start space-x-6\"\n                >\n                  {/* Timeline Dot */}\n                  <div className=\"relative z-10 w-16 h-16 bg-white border-4 border-brand-navy rounded-full flex items-center justify-center shadow-lg\">\n                    <milestone.icon className=\"h-6 w-6 text-brand-navy\" />\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"flex-1 bg-gray-50 rounded-2xl p-6\">\n                    <div className=\"text-brand-gold font-bold text-lg mb-2\">\n                      {milestone.year}\n                    </div>\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                      {milestone.title}\n                    </h3>\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {milestone.description}\n                    </p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Nepal Focus Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mt-20\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white\">\n            <div className=\"max-w-4xl mx-auto text-center\">\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n                Why We Focus on Nepal\n              </h2>\n              <p className=\"text-blue-100 text-lg mb-8 leading-relaxed\">\n                Nepal is our home, and we believe in the incredible potential of Nepali businesses. \n                Our deep understanding of local culture, consumer behavior, and market dynamics \n                allows us to create digital marketing strategies that truly resonate.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-brand-gold mb-2\">100%</div>\n                  <div className=\"text-blue-100\">Local Team</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-brand-gold mb-2\">5+</div>\n                  <div className=\"text-blue-100\">Years in Nepal</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-brand-gold mb-2\">150+</div>\n                  <div className=\"text-blue-100\">Nepali Businesses Served</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,4MAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;IACd;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;IACb;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAI,WAAU;8CAA4G;;;;;;8CAI3H,8OAAC;oCAAG,WAAU;;wCAAoD;sDAEhE,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAG1C,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAM1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAOjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAOjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,WAAW,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAGV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,UAAU,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAI5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,UAAU,IAAI;;;;;;sEAEjB,8OAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,8OAAC;4DAAE,WAAU;sEACV,UAAU,WAAW;;;;;;;;;;;;;2CArBrB,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;8BA+B7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAM1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/loading.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nexport function Loading({ size = 'md', className, text }: LoadingProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>\n      <motion.div\n        className={cn(\n          'border-4 border-gray-200 border-t-brand-navy rounded-full',\n          sizeClasses[size]\n        )}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: 'linear',\n        }}\n      />\n      {text && (\n        <motion.p\n          className=\"text-gray-600 text-sm\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  );\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <motion.div\n      className={cn('bg-gray-200 rounded', className)}\n      animate={{\n        opacity: [0.5, 1, 0.5],\n      }}\n      transition={{\n        duration: 1.5,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      }}\n    />\n  );\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"p-6 border rounded-lg space-y-4\">\n      <LoadingSkeleton className=\"h-4 w-3/4\" />\n      <LoadingSkeleton className=\"h-3 w-full\" />\n      <LoadingSkeleton className=\"h-3 w-2/3\" />\n      <LoadingSkeleton className=\"h-8 w-24\" />\n    </div>\n  );\n}\n\nexport function LoadingGrid({ count = 6 }: { count?: number }) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {Array.from({ length: count }).map((_, i) => (\n        <LoadingCard key={i} />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAWO,SAAS,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAgB;IACpE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;0BACxE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6DACA,WAAW,CAAC,KAAK;gBAEnB,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;YAED,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAExB;;;;;;;;;;;;AAKX;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACrC,SAAS;YACP,SAAS;gBAAC;gBAAK;gBAAG;aAAI;QACxB;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;;;;;;;AAGjC;AAEO,SAAS,YAAY,EAAE,QAAQ,CAAC,EAAsB;IAC3D,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,8OAAC,iBAAiB;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/team-section.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Linkedin, Mail, Star, Users } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { useTeamMembers } from '@/lib/queries/hooks';\nimport { Loading } from '@/components/ui/loading';\n\nexport default function TeamSection() {\n  const { data: team, isLoading, error } = useTeamMembers();\n\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center\">\n            <Loading size=\"lg\" text=\"Loading team members...\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error || !team) {\n    return null;\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\" id=\"team\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            <Users className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Meet Our Team\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            The Experts Behind\n            <span className=\"block text-brand-navy\">Your Success</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Our diverse team of digital marketing professionals brings together years of experience, \n            creative thinking, and deep understanding of the Nepali market to deliver exceptional results.\n          </p>\n        </motion.div>\n\n        {/* Team Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {team.map((member, index) => (\n            <motion.div\n              key={member.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <Card className=\"h-full border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 overflow-hidden\">\n                {/* Profile Image */}\n                <div className=\"relative h-64 bg-gradient-to-br from-brand-navy to-brand-gold overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-32 h-32 bg-white rounded-full flex items-center justify-center text-4xl font-bold text-brand-navy\">\n                      {member.name.split(' ').map(n => n[0]).join('')}\n                    </div>\n                  </div>\n                  \n                  {/* Social Links Overlay */}\n                  <div className=\"absolute inset-0 bg-brand-navy/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4\">\n                    {member.social?.linkedin && (\n                      <a\n                        href={member.social.linkedin}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"w-12 h-12 bg-white rounded-full flex items-center justify-center text-brand-navy hover:bg-brand-gold hover:text-gray-900 transition-colors\"\n                      >\n                        <Linkedin className=\"h-5 w-5\" />\n                      </a>\n                    )}\n                    {member.social?.email && (\n                      <a\n                        href={`mailto:${member.social.email}`}\n                        className=\"w-12 h-12 bg-white rounded-full flex items-center justify-center text-brand-navy hover:bg-brand-gold hover:text-gray-900 transition-colors\"\n                      >\n                        <Mail className=\"h-5 w-5\" />\n                      </a>\n                    )}\n                  </div>\n                </div>\n\n                <CardContent className=\"p-6\">\n                  <div className=\"text-center mb-4\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-1\">\n                      {member.name}\n                    </h3>\n                    <p className=\"text-brand-navy font-semibold mb-2\">\n                      {member.position}\n                    </p>\n                    <div className=\"flex items-center justify-center space-x-2 text-sm text-gray-600\">\n                      <Star className=\"h-4 w-4 text-brand-gold\" />\n                      <span>{member.experience} experience</span>\n                    </div>\n                  </div>\n\n                  <p className=\"text-gray-600 text-sm mb-4 leading-relaxed\">\n                    {member.bio}\n                  </p>\n\n                  {/* Expertise Tags */}\n                  <div className=\"mb-4\">\n                    <div className=\"text-sm font-semibold text-gray-900 mb-2\">Expertise:</div>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {member.expertise.map((skill, skillIndex) => (\n                        <Badge key={skillIndex} variant=\"secondary\" className=\"text-xs\">\n                          {skill}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Education */}\n                  {member.education && (\n                    <div className=\"text-xs text-gray-500 border-t pt-3\">\n                      <strong>Education:</strong> {member.education}\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Team Culture Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-white rounded-3xl p-8 md:p-12\"\n        >\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Our Team Culture\n            </h3>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              We believe that great results come from great people working together with shared values and vision.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {[\n              {\n                title: 'Collaboration',\n                description: 'We work together as one team, sharing knowledge and supporting each other to achieve the best results for our clients.',\n                icon: '🤝'\n              },\n              {\n                title: 'Innovation',\n                description: 'We stay ahead of digital marketing trends and continuously learn new strategies to keep our clients competitive.',\n                icon: '💡'\n              },\n              {\n                title: 'Integrity',\n                description: 'We believe in honest communication, transparent reporting, and always doing what\\'s best for our clients\\' success.',\n                icon: '🎯'\n              }\n            ].map((value, index) => (\n              <motion.div\n                key={value.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center\"\n              >\n                <div className=\"text-4xl mb-4\">{value.icon}</div>\n                <h4 className=\"text-xl font-bold text-gray-900 mb-3\">{value.title}</h4>\n                <p className=\"text-gray-600 leading-relaxed\">{value.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Join Team CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n              Want to Join Our Team?\n            </h3>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              We&apos;re always looking for talented individuals who are passionate about digital marketing \n              and want to make a difference in Nepal&apos;s business landscape.\n            </p>\n            <Button \n              asChild \n              size=\"lg\" \n              className=\"bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold\"\n            >\n              <a href=\"mailto:<EMAIL>\">\n                View Open Positions\n              </a>\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEtD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAK;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;IAKlC;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAmB,IAAG;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGpD,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,QAAQ,sBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;0DAKhD,8OAAC;gDAAI,WAAU;;oDACZ,OAAO,MAAM,EAAE,0BACd,8OAAC;wDACC,MAAM,OAAO,MAAM,CAAC,QAAQ;wDAC5B,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;oDAGvB,OAAO,MAAM,EAAE,uBACd,8OAAC;wDACC,MAAM,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE;wDACrC,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMxB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAE,WAAU;kEACV,OAAO,QAAQ;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;;oEAAM,OAAO,UAAU;oEAAC;;;;;;;;;;;;;;;;;;;0DAI7B,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG;;;;;;0DAIb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;kEAC1D,8OAAC;wDAAI,WAAU;kEACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC5B,8OAAC,iIAAA,CAAA,QAAK;gEAAkB,SAAQ;gEAAY,WAAU;0EACnD;+DADS;;;;;;;;;;;;;;;;4CAQjB,OAAO,SAAS,kBACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAO;;;;;;oDAAmB;oDAAE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;2BAzEhD,OAAO,EAAE;;;;;;;;;;8BAmFpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;gCACA;oCACE,OAAO;oCACP,aAAa;oCACb,MAAM;gCACR;6BACD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAiB,MAAM,IAAI;;;;;;sDAC1C,8OAAC;4CAAG,WAAU;sDAAwC,MAAM,KAAK;;;;;;sDACjE,8OAAC;4CAAE,WAAU;sDAAiC,MAAM,WAAW;;;;;;;mCAT1D,MAAM,KAAK;;;;;;;;;;;;;;;;8BAgBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAE,MAAK;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/about-values.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Shield, Target, Users, Zap, Heart, Award } from 'lucide-react';\nimport { useCompanyInfo } from '@/lib/queries/hooks';\n\nconst values = [\n  {\n    icon: Shield,\n    title: 'Integrity & Transparency',\n    description: 'We believe in honest communication, clear reporting, and always putting our clients\\' best interests first. No hidden costs, no false promises.',\n    color: 'from-blue-500 to-blue-600'\n  },\n  {\n    icon: Target,\n    title: 'Results-Driven Approach',\n    description: 'Every strategy we create is focused on delivering measurable results that directly impact your business growth and success.',\n    color: 'from-green-500 to-green-600'\n  },\n  {\n    icon: Users,\n    title: 'Client Partnership',\n    description: 'We see ourselves as partners in your success, working closely with you to understand your goals and challenges.',\n    color: 'from-purple-500 to-purple-600'\n  },\n  {\n    icon: Zap,\n    title: 'Innovation & Excellence',\n    description: 'We stay ahead of digital marketing trends and continuously improve our strategies to keep our clients competitive.',\n    color: 'from-yellow-500 to-orange-500'\n  },\n  {\n    icon: Heart,\n    title: 'Local Market Focus',\n    description: 'Our deep understanding of Nepali culture, consumer behavior, and market dynamics drives our strategic decisions.',\n    color: 'from-red-500 to-pink-500'\n  },\n  {\n    icon: Award,\n    title: 'Quality Assurance',\n    description: 'We maintain the highest standards in everything we do, from strategy development to campaign execution and reporting.',\n    color: 'from-indigo-500 to-purple-500'\n  }\n];\n\nexport default function AboutValues() {\n  const { data: company } = useCompanyInfo();\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            Our Values\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            What Drives Us\n            <span className=\"block text-brand-navy\">Every Day</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Our core values shape everything we do, from how we work with clients to how we \n            approach digital marketing challenges. These principles guide our decisions and define our culture.\n          </p>\n        </motion.div>\n\n        {/* Values Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {values.map((value, index) => (\n            <motion.div\n              key={value.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100 h-full\">\n                <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                  <value.icon className=\"h-8 w-8 text-white\" />\n                </div>\n                \n                <h3 className=\"text-xl font-bold text-gray-900 mb-4 group-hover:text-brand-navy transition-colors\">\n                  {value.title}\n                </h3>\n                \n                <p className=\"text-gray-600 leading-relaxed\">\n                  {value.description}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Vision Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-gray-50 rounded-3xl p-8 md:p-12\"\n        >\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-6\">\n              Our Vision for Nepal&apos;s Digital Future\n            </h3>\n            {company && (\n              <p className=\"text-lg text-gray-700 mb-8 leading-relaxed\">\n                {company.vision}\n              </p>\n            )}\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-brand-navy rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <Target className=\"h-8 w-8 text-white\" />\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Empower Businesses</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  Help every Nepali business reach its full potential in the digital world\n                </p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <Zap className=\"h-8 w-8 text-gray-900\" />\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Drive Innovation</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  Bring cutting-edge digital marketing strategies to the Nepali market\n                </p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <Heart className=\"h-8 w-8 text-white\" />\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Build Community</h4>\n                <p className=\"text-gray-600 text-sm\">\n                  Create a thriving ecosystem of digitally empowered Nepali businesses\n                </p>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Commitment Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white\">\n            <div className=\"max-w-4xl mx-auto text-center\">\n              <h3 className=\"text-2xl md:text-3xl font-bold mb-6\">\n                Our Commitment to You\n              </h3>\n              <p className=\"text-blue-100 text-lg mb-8 leading-relaxed\">\n                When you choose Lunar Cubes, you&apos;re not just hiring a digital marketing agency. \n                You&apos;re partnering with a team that is genuinely invested in your success and \n                committed to helping your business thrive in Nepal&apos;s digital landscape.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6\">\n                  <h4 className=\"text-xl font-bold mb-3\">24/7 Support</h4>\n                  <p className=\"text-blue-100\">\n                    We&apos;re always here when you need us, providing ongoing support and guidance \n                    for your digital marketing journey.\n                  </p>\n                </div>\n                \n                <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6\">\n                  <h4 className=\"text-xl font-bold mb-3\">Continuous Learning</h4>\n                  <p className=\"text-blue-100\">\n                    We stay updated with the latest trends and technologies to ensure your \n                    strategies remain effective and competitive.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEvC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCAA4G;;;;;;sCAG3H,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,MAAM,KAAK,CAAC,0GAA0G,CAAC;kDACnK,cAAA,8OAAC,MAAM,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAGxB,8OAAC;wCAAG,WAAU;kDACX,MAAM,KAAK;;;;;;kDAGd,8OAAC;wCAAE,WAAU;kDACV,MAAM,WAAW;;;;;;;;;;;;2BAjBjB,MAAM,KAAK;;;;;;;;;;8BAyBtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;4BAGjE,yBACC,8OAAC;gCAAE,WAAU;0CACV,QAAQ,MAAM;;;;;;0CAInB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAM1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAM/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/C", "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/about-stats.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { TrendingUp, Users, Award, MapPin, Clock, Star } from 'lucide-react';\nimport { useCompanyInfo } from '@/lib/queries/hooks';\n\nexport default function AboutStats() {\n  const { data: company } = useCompanyInfo();\n\n  const achievements = [\n    {\n      icon: Users,\n      value: company?.stats.clientsServed || 150,\n      suffix: '+',\n      label: 'Happy Clients',\n      description: 'Businesses transformed across Nepal'\n    },\n    {\n      icon: Award,\n      value: company?.stats.projectsCompleted || 300,\n      suffix: '+',\n      label: 'Projects Completed',\n      description: 'Successful digital campaigns delivered'\n    },\n    {\n      icon: TrendingUp,\n      value: 500,\n      suffix: '%',\n      label: 'Average Growth',\n      description: 'Increase in digital presence'\n    },\n    {\n      icon: Clock,\n      value: company?.stats.yearsExperience || 5,\n      suffix: '+',\n      label: 'Years Experience',\n      description: 'Serving the Nepali market'\n    },\n    {\n      icon: Star,\n      value: 98,\n      suffix: '%',\n      label: 'Client Satisfaction',\n      description: 'Based on client feedback surveys'\n    },\n    {\n      icon: MapPin,\n      value: 7,\n      suffix: '',\n      label: 'Cities Served',\n      description: 'Across Nepal and beyond'\n    }\n  ];\n\n  const milestones = [\n    {\n      year: '2019',\n      title: 'Company Founded',\n      description: 'Started our journey in Kathmandu'\n    },\n    {\n      year: '2020',\n      title: 'First 50 Clients',\n      description: 'Reached our first major milestone'\n    },\n    {\n      year: '2021',\n      title: 'Team Expansion',\n      description: 'Grew to 8+ team members'\n    },\n    {\n      year: '2022',\n      title: '100+ Projects',\n      description: 'Completed our 100th project'\n    },\n    {\n      year: '2023',\n      title: 'Industry Recognition',\n      description: 'Recognized as leading agency'\n    },\n    {\n      year: '2024',\n      title: 'Market Leader',\n      description: 'Established as Nepal\\'s premier agency'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            <TrendingUp className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Our Impact\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Numbers That Tell\n            <span className=\"block text-brand-navy\">Our Story</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            These numbers represent more than just statistics – they represent the trust our clients \n            have placed in us and the impact we&apos;ve made on Nepal&apos;s digital landscape.\n          </p>\n        </motion.div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {achievements.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 text-center h-full\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <stat.icon className=\"h-8 w-8 text-white\" />\n                </div>\n                \n                <div className=\"text-4xl md:text-5xl font-bold text-brand-navy mb-2\">\n                  <motion.span\n                    initial={{ opacity: 0 }}\n                    whileInView={{ opacity: 1 }}\n                    transition={{ duration: 1, delay: 0.5 + index * 0.1 }}\n                    viewport={{ once: true }}\n                  >\n                    {stat.value}{stat.suffix}\n                  </motion.span>\n                </div>\n                \n                <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n                  {stat.label}\n                </h3>\n                \n                <p className=\"text-gray-600 text-sm\">\n                  {stat.description}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Timeline Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-white rounded-3xl p-8 md:p-12 mb-16\"\n        >\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Our Journey Through the Years\n            </h3>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              From a small startup to Nepal&apos;s leading digital marketing agency, \n              here are the key milestones that shaped our journey.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {milestones.map((milestone, index) => (\n              <motion.div\n                key={milestone.year}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center p-6 rounded-xl bg-gray-50 hover:bg-brand-navy hover:text-white transition-all duration-300 group\"\n              >\n                <div className=\"text-2xl font-bold text-brand-gold mb-2 group-hover:text-brand-gold\">\n                  {milestone.year}\n                </div>\n                <h4 className=\"text-lg font-semibold mb-2 group-hover:text-white\">\n                  {milestone.title}\n                </h4>\n                <p className=\"text-sm text-gray-600 group-hover:text-blue-100\">\n                  {milestone.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Recognition Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-6\">\n              Trusted by Leading Businesses\n            </h3>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Our success is measured by the success of our clients. We&apos;re proud to have worked \n              with some of Nepal&apos;s most innovative and forward-thinking businesses.\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {[\n                { label: 'Hospitality', count: '40+' },\n                { label: 'E-commerce', count: '35+' },\n                { label: 'Healthcare', count: '25+' },\n                { label: 'Technology', count: '20+' }\n              ].map((industry, index) => (\n                <motion.div\n                  key={industry.label}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center\"\n                >\n                  <div className=\"text-3xl font-bold text-brand-gold mb-2\">\n                    {industry.count}\n                  </div>\n                  <div className=\"text-blue-100\">\n                    {industry.label} Clients\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEvC,MAAM,eAAe;QACnB;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,SAAS,MAAM,iBAAiB;YACvC,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,SAAS,MAAM,qBAAqB;YAC3C,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,SAAS,MAAM,mBAAmB;YACzC,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGzD,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,aAAa;gDAAE,SAAS;4CAAE;4CAC1B,YAAY;gDAAE,UAAU;gDAAG,OAAO,MAAM,QAAQ;4CAAI;4CACpD,UAAU;gDAAE,MAAM;4CAAK;;gDAEtB,KAAK,KAAK;gDAAE,KAAK,MAAM;;;;;;;;;;;;kDAI5B,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAGb,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;2BA5BhB,KAAK,KAAK;;;;;;;;;;8BAoCrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACZ,UAAU,IAAI;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDACX,UAAU,KAAK;;;;;;sDAElB,8OAAC;4CAAE,WAAU;sDACV,UAAU,WAAW;;;;;;;mCAdnB,UAAU,IAAI;;;;;;;;;;;;;;;;8BAsB3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAe,OAAO;oCAAM;oCACrC;wCAAE,OAAO;wCAAc,OAAO;oCAAM;oCACpC;wCAAE,OAAO;wCAAc,OAAO;oCAAM;oCACpC;wCAAE,OAAO;wCAAc,OAAO;oCAAM;iCACrC,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,SAAS,KAAK;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,KAAK;oDAAC;;;;;;;;uCAXb,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBrC", "debugId": null}}]}