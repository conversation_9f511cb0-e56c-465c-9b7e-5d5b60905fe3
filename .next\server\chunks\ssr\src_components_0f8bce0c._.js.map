{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ArrowRight, Play, Star, Users, Award, TrendingUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useCompanyInfo } from '@/lib/queries/hooks';\n\nexport default function Hero() {\n  const { data: company } = useCompanyInfo();\n\n  const stats = [\n    { icon: Users, label: 'Happy Clients', value: company?.stats.clientsServed || 150 },\n    { icon: Award, label: 'Projects Completed', value: company?.stats.projectsCompleted || 300 },\n    { icon: TrendingUp, label: 'Years Experience', value: company?.stats.yearsExperience || 5 },\n    { icon: Star, label: 'Team Members', value: company?.stats.teamMembers || 12 },\n  ];\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0\">\n        <Image\n          src=\"/images/hero-bg.svg\"\n          alt=\"Digital Marketing Background\"\n          fill\n          className=\"object-cover\"\n          priority\n        />\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-brand-gold/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-brand-navy/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-brand-navy/5 to-brand-gold/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-20 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center lg:text-left\"\n          >\n            {/* Badge */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\"\n            >\n              <Star className=\"w-4 h-4 mr-2 text-brand-gold\" />\n              Nepal&apos;s Premier Digital Marketing Agency\n            </motion.div>\n\n            {/* Main Heading */}\n            <motion.h1\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\"\n            >\n              <span className=\"text-brand-navy\">Lunar Cubes</span>\n              <br />\n              <span className=\"bg-gradient-to-r from-brand-gold to-yellow-500 bg-clip-text text-transparent\">\n                From Concept to Cosmos\n              </span>\n            </motion.h1>\n\n            {/* Subtitle */}\n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"text-lg md:text-xl text-gray-600 mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed\"\n            >\n              Empowering Nepali SMEs with cutting-edge digital marketing strategies. \n              We transform your business vision into digital success stories that reach new heights.\n            </motion.p>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.5 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12\"\n            >\n              <Button \n                asChild \n                size=\"lg\" \n                className=\"bg-brand-navy hover:bg-brand-navy-dark text-white px-8 py-4 text-lg font-semibold group\"\n              >\n                <Link href=\"/contact\">\n                  Start Your Journey\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </Button>\n              \n              <Button \n                variant=\"outline\" \n                size=\"lg\" \n                className=\"border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white px-8 py-4 text-lg font-semibold group\"\n              >\n                <Play className=\"mr-2 h-5 w-5\" />\n                Watch Our Story\n              </Button>\n            </motion.div>\n\n            {/* Stats */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"grid grid-cols-2 md:grid-cols-4 gap-6\"\n            >\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}\n                  className=\"text-center\"\n                >\n                  <div className=\"flex justify-center mb-2\">\n                    <stat.icon className=\"h-8 w-8 text-brand-gold\" />\n                  </div>\n                  <div className=\"text-2xl md:text-3xl font-bold text-brand-navy mb-1\">\n                    {stat.value}+\n                  </div>\n                  <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Right Content - Hero Image/Illustration */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"relative\"\n          >\n            <div className=\"relative z-10\">\n              {/* Hero Image */}\n              <div className=\"relative rounded-3xl overflow-hidden shadow-2xl mb-6\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\n                  alt=\"Digital Marketing Team Working\"\n                  width={500}\n                  height={300}\n                  className=\"object-cover w-full h-64\"\n                  priority\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-brand-navy/80 via-transparent to-transparent\"></div>\n                <div className=\"absolute bottom-4 left-4 text-white\">\n                  <div className=\"text-sm opacity-90\">Our Expert Team</div>\n                  <div className=\"text-lg font-bold\">Delivering Results</div>\n                </div>\n              </div>\n\n              {/* Main Hero Visual */}\n              <div className=\"relative bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-3xl p-8 shadow-2xl\">\n                <div className=\"bg-white rounded-2xl p-6 mb-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"text-sm font-semibold text-gray-600\">Digital Growth</div>\n                    <div className=\"text-2xl font-bold text-brand-navy\">+250%</div>\n                  </div>\n                  <div className=\"h-2 bg-gray-200 rounded-full overflow-hidden\">\n                    <motion.div\n                      initial={{ width: 0 }}\n                      animate={{ width: '85%' }}\n                      transition={{ duration: 2, delay: 1 }}\n                      className=\"h-full bg-gradient-to-r from-brand-navy to-brand-gold rounded-full\"\n                    ></motion.div>\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4 text-white\">\n                    <div className=\"text-sm opacity-80\">Social Media</div>\n                    <div className=\"text-xl font-bold\">15K+</div>\n                    <div className=\"text-xs opacity-60\">Followers</div>\n                  </div>\n                  <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4 text-white\">\n                    <div className=\"text-sm opacity-80\">Website Traffic</div>\n                    <div className=\"text-xl font-bold\">500%</div>\n                    <div className=\"text-xs opacity-60\">Increase</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -right-4 bg-brand-gold text-white p-3 rounded-full shadow-lg\"\n              >\n                <TrendingUp className=\"h-6 w-6\" />\n              </motion.div>\n\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-4 -left-4 bg-white text-brand-navy p-3 rounded-full shadow-lg\"\n              >\n                <Award className=\"h-6 w-6\" />\n              </motion.div>\n            </div>\n\n            {/* Background Decoration */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-brand-gold/20 to-brand-navy/20 rounded-3xl transform rotate-6 scale-105 -z-10\"></div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-brand-navy rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-brand-navy rounded-full mt-2\"\n          ></motion.div>\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEvC,MAAM,QAAQ;QACZ;YAAE,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAiB,OAAO,SAAS,MAAM,iBAAiB;QAAI;QAClF;YAAE,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAsB,OAAO,SAAS,MAAM,qBAAqB;QAAI;QAC3F;YAAE,MAAM,kNAAA,CAAA,aAAU;YAAE,OAAO;YAAoB,OAAO,SAAS,MAAM,mBAAmB;QAAE;QAC1F;YAAE,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAgB,OAAO,SAAS,MAAM,eAAe;QAAG;KAC9E;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;oBACV,QAAQ;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiC;;;;;;;8CAKnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAkB;;;;;;sDAClC,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAA+E;;;;;;;;;;;;8CAMjG,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;8CAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;;oDAAW;kEAEpB,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAI1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK;wDAAC;;;;;;;8DAEd,8OAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;;2CAZ7C,KAAK,KAAK;;;;;;;;;;;;;;;;sCAmBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,QAAQ;;;;;;8DAEV,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;sEAAoB;;;;;;;;;;;;;;;;;;sDAKvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;;;;;;;sEAEtD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEAAE,OAAO;gEAAM;gEACxB,YAAY;oEAAE,UAAU;oEAAG,OAAO;gEAAE;gEACpC,WAAU;;;;;;;;;;;;;;;;;8DAKhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;8EACpC,8OAAC;oEAAI,WAAU;8EAAoB;;;;;;8EACnC,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;8EACpC,8OAAC;oEAAI,WAAU;8EAAoB;;;;;;8EACnC,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;sDAM1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC,CAAC;oDAAI;oDAAI,CAAC;iDAAG;4CAAC;4CAC7B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;4CAC/D,WAAU;sDAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAI,CAAC;oDAAI;iDAAG;4CAAC;4CAC5B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;4CAC/D,WAAU;sDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKrB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/why-choose-us.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  MapPin, \n  Users, \n  TrendingUp, \n  Shield, \n  Clock, \n  Award,\n  CheckCircle,\n  Star\n} from 'lucide-react';\n\nconst features = [\n  {\n    icon: MapPin,\n    title: 'Local Market Expertise',\n    description: 'Deep understanding of Nepali consumer behavior, cultural nuances, and market dynamics that drive successful campaigns.',\n    stats: '5+ years in Nepal market'\n  },\n  {\n    icon: Users,\n    title: 'Dedicated Team',\n    description: 'Experienced professionals who speak your language and understand your business challenges in the local context.',\n    stats: '12+ expert team members'\n  },\n  {\n    icon: TrendingUp,\n    title: 'Proven Results',\n    description: 'Track record of delivering measurable growth for 150+ Nepali businesses across various industries.',\n    stats: '300+ successful projects'\n  },\n  {\n    icon: Shield,\n    title: 'Transparent Approach',\n    description: 'Clear communication, detailed reporting, and honest advice. No hidden costs or unrealistic promises.',\n    stats: '100% transparency guarantee'\n  },\n  {\n    icon: Clock,\n    title: 'Quick Response',\n    description: 'Fast turnaround times and responsive support. We understand the pace of business in Nepal.',\n    stats: '24-hour response time'\n  },\n  {\n    icon: Award,\n    title: 'Quality Assurance',\n    description: 'Rigorous quality checks and continuous optimization to ensure your campaigns deliver maximum ROI.',\n    stats: '98% client satisfaction'\n  }\n];\n\nconst achievements = [\n  {\n    number: '150+',\n    label: 'Happy Clients',\n    description: 'Businesses transformed'\n  },\n  {\n    number: '300+',\n    label: 'Projects Completed',\n    description: 'Successful campaigns'\n  },\n  {\n    number: '500%',\n    label: 'Average Growth',\n    description: 'In digital presence'\n  },\n  {\n    number: '5+',\n    label: 'Years Experience',\n    description: 'In Nepal market'\n  }\n];\n\nexport default function WhyChooseUs() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            <Star className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Why Choose Lunar Cubes\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Your Success is Our\n            <span className=\"block text-brand-navy\">Mission</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            We&apos;re not just another digital marketing agency. We&apos;re your partners in growth, \n            committed to understanding your unique challenges and delivering solutions that work in Nepal.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center mb-20\">\n          {/* Left Content - Features */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"mb-8\"\n            >\n              <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n                What Makes Us Different\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                Our unique combination of global digital marketing expertise and deep local market \n                knowledge sets us apart from other agencies in Nepal.\n              </p>\n            </motion.div>\n\n            <div className=\"space-y-6\">\n              {features.map((feature, index) => (\n                <motion.div\n                  key={feature.title}\n                  initial={{ opacity: 0, x: -30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-start space-x-4 group\"\n                >\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                    <feature.icon className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {feature.title}\n                    </h4>\n                    <p className=\"text-gray-600 mb-2 leading-relaxed\">\n                      {feature.description}\n                    </p>\n                    <div className=\"text-sm font-medium text-brand-navy\">\n                      {feature.stats}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Right Content - Visual */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            {/* Main Card */}\n            <div className=\"bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-3xl p-8 text-white relative overflow-hidden\">\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16\"></div>\n                <div className=\"absolute bottom-0 left-0 w-24 h-24 bg-brand-gold rounded-full translate-y-12 -translate-x-12\"></div>\n              </div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center mr-4\">\n                    <Award className=\"h-6 w-6 text-gray-900\" />\n                  </div>\n                  <div>\n                    <h4 className=\"text-xl font-bold\">Certified Excellence</h4>\n                    <p className=\"text-blue-100\">Google & Facebook Partners</p>\n                  </div>\n                </div>\n\n                <div className=\"space-y-4 mb-6\">\n                  {[\n                    'Google Ads Certified Professionals',\n                    'Facebook Marketing Partners',\n                    'HubSpot Certified Agency',\n                    'Local Business Specialists'\n                  ].map((certification, index) => (\n                    <motion.div\n                      key={certification}\n                      initial={{ opacity: 0, x: 20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}\n                      viewport={{ once: true }}\n                      className=\"flex items-center\"\n                    >\n                      <CheckCircle className=\"h-5 w-5 text-brand-gold mr-3 flex-shrink-0\" />\n                      <span className=\"text-blue-100\">{certification}</span>\n                    </motion.div>\n                  ))}\n                </div>\n\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4\">\n                  <div className=\"text-sm text-blue-100 mb-2\">Client Satisfaction Rate</div>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-2xl font-bold\">98%</span>\n                    <div className=\"flex space-x-1\">\n                      {[...Array(5)].map((_, i) => (\n                        <Star key={i} className=\"h-4 w-4 text-brand-gold fill-current\" />\n                      ))}\n                    </div>\n                  </div>\n                  <div className=\"w-full bg-white/20 rounded-full h-2\">\n                    <motion.div\n                      initial={{ width: 0 }}\n                      whileInView={{ width: '98%' }}\n                      transition={{ duration: 2, delay: 1 }}\n                      viewport={{ once: true }}\n                      className=\"bg-brand-gold h-2 rounded-full\"\n                    ></motion.div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Floating Achievement Cards */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              viewport={{ once: true }}\n              className=\"absolute -top-6 -left-6 bg-white rounded-2xl p-4 shadow-xl border\"\n            >\n              <div className=\"text-2xl font-bold text-brand-navy\">150+</div>\n              <div className=\"text-sm text-gray-600\">Happy Clients</div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 1 }}\n              viewport={{ once: true }}\n              className=\"absolute -bottom-6 -right-6 bg-brand-gold rounded-2xl p-4 shadow-xl\"\n            >\n              <div className=\"text-2xl font-bold text-gray-900\">5+</div>\n              <div className=\"text-sm text-gray-700\">Years Experience</div>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        {/* Achievements Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8\"\n        >\n          {achievements.map((achievement, index) => (\n            <motion.div\n              key={achievement.label}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl md:text-4xl font-bold text-brand-navy mb-2\">\n                {achievement.number}\n              </div>\n              <div className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {achievement.label}\n              </div>\n              <div className=\"text-sm text-gray-600\">\n                {achievement.description}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAcA,MAAM,WAAW;IACf;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IACA;QACE,QAAQ;QACR,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGnD,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,KAAK;;;;;;;;;;;;;2CAlBb,QAAQ,KAAK;;;;;;;;;;;;;;;;sCA2B1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoB;;;;;;8EAClC,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAIjC,8OAAC;oDAAI,WAAU;8DACZ;wDACC;wDACA;wDACA;wDACA;qDACD,CAAC,GAAG,CAAC,CAAC,eAAe,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,aAAa;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAChC,YAAY;gEAAE,UAAU;gEAAK,OAAO,MAAM,QAAQ;4DAAI;4DACtD,UAAU;gEAAE,MAAM;4DAAK;4DACvB,WAAU;;8EAEV,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;;2DAR5B;;;;;;;;;;8DAaX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA6B;;;;;;sEAC5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;8EACrC,8OAAC;oEAAI,WAAU;8EACZ;2EAAI,MAAM;qEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4EAAS,WAAU;2EAAb;;;;;;;;;;;;;;;;sEAIjB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,OAAO;gEAAE;gEACpB,aAAa;oEAAE,OAAO;gEAAM;gEAC5B,YAAY;oEAAE,UAAU;oEAAG,OAAO;gEAAE;gEACpC,UAAU;oEAAE,MAAM;gEAAK;gEACvB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAE;oCACtC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;8BAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,YAAY,MAAM;;;;;;8CAErB,8OAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK;;;;;;8CAEpB,8OAAC;oCAAI,WAAU;8CACZ,YAAY,WAAW;;;;;;;2BAdrB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;AAsBpC", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/loading.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nexport function Loading({ size = 'md', className, text }: LoadingProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>\n      <motion.div\n        className={cn(\n          'border-4 border-gray-200 border-t-brand-navy rounded-full',\n          sizeClasses[size]\n        )}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: 'linear',\n        }}\n      />\n      {text && (\n        <motion.p\n          className=\"text-gray-600 text-sm\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  );\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <motion.div\n      className={cn('bg-gray-200 rounded', className)}\n      animate={{\n        opacity: [0.5, 1, 0.5],\n      }}\n      transition={{\n        duration: 1.5,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      }}\n    />\n  );\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"p-6 border rounded-lg space-y-4\">\n      <LoadingSkeleton className=\"h-4 w-3/4\" />\n      <LoadingSkeleton className=\"h-3 w-full\" />\n      <LoadingSkeleton className=\"h-3 w-2/3\" />\n      <LoadingSkeleton className=\"h-8 w-24\" />\n    </div>\n  );\n}\n\nexport function LoadingGrid({ count = 6 }: { count?: number }) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {Array.from({ length: count }).map((_, i) => (\n        <LoadingCard key={i} />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAWO,SAAS,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAgB;IACpE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;0BACxE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6DACA,WAAW,CAAC,KAAK;gBAEnB,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;YAED,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAExB;;;;;;;;;;;;AAKX;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACrC,SAAS;YACP,SAAS;gBAAC;gBAAK;gBAAG;aAAI;QACxB;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;;;;;;;AAGjC;AAEO,SAAS,YAAY,EAAE,QAAQ,CAAC,EAAsB;IAC3D,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,8OAAC,iBAAiB;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 1545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/services-overview.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ArrowRight, Share2, Search, Code, Target, PenTool, Palette } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useServices } from '@/lib/queries/hooks';\nimport { Loading } from '@/components/ui/loading';\n\nconst iconMap = {\n  Share2,\n  Search,\n  Code,\n  Target,\n  PenTool,\n  Palette,\n};\n\n// Helper function to get service images from Unsplash\nconst getServiceImage = (title: string) => {\n  const serviceImages: { [key: string]: string } = {\n    'Social Media Marketing': 'https://images.unsplash.com/photo-1611926653458-09294b3142bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    'SEO Optimization': 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    'Web Development': 'https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    'Google Ads': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    'Content Marketing': 'https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    'Brand Identity': 'https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n  };\n  return serviceImages[title] || 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80';\n};\n\nexport default function ServicesOverview() {\n  const { data: services, isLoading, error } = useServices();\n\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <Loading size=\"lg\" text=\"Loading our services...\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error || !services) {\n    return (\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <p className=\"text-red-600\">Failed to load services. Please try again later.</p>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            Our Expertise\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Comprehensive Digital Marketing\n            <span className=\"block text-brand-navy\">Solutions for Nepal</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            From social media management to advanced SEO strategies, we offer a complete suite \n            of digital marketing services tailored specifically for the Nepali market.\n          </p>\n        </motion.div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {services.map((service, index) => {\n            const IconComponent = iconMap[service.icon as keyof typeof iconMap] || Share2;\n            \n            return (\n              <motion.div\n                key={service.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card className=\"h-full group hover:shadow-xl transition-all duration-300 border-0 shadow-md hover:-translate-y-2 overflow-hidden\">\n                  {/* Service Image */}\n                  <div className=\"relative h-48 overflow-hidden\">\n                    <Image\n                      src={getServiceImage(service.title)}\n                      alt={`${service.title} service`}\n                      fill\n                      className=\"object-cover group-hover:scale-110 transition-transform duration-300\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"></div>\n                    <div className=\"absolute bottom-4 left-4\">\n                      <div className=\"w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center mb-2\">\n                        <IconComponent className=\"h-6 w-6 text-brand-navy\" />\n                      </div>\n                    </div>\n                  </div>\n\n                  <CardHeader className=\"pb-4\">\n                    <CardTitle className=\"text-xl font-bold text-gray-900 group-hover:text-brand-navy transition-colors\">\n                      {service.title}\n                    </CardTitle>\n                    <CardDescription className=\"text-gray-600 leading-relaxed\">\n                      {service.shortDescription}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"pt-0\">\n                    <div className=\"space-y-3 mb-6\">\n                      {service.features.slice(0, 3).map((feature, featureIndex) => (\n                        <div key={featureIndex} className=\"flex items-center text-sm text-gray-600\">\n                          <div className=\"w-2 h-2 bg-brand-gold rounded-full mr-3 flex-shrink-0\"></div>\n                          {feature}\n                        </div>\n                      ))}\n                      {service.features.length > 3 && (\n                        <div className=\"text-sm text-brand-navy font-medium\">\n                          +{service.features.length - 3} more features\n                        </div>\n                      )}\n                    </div>\n                    \n                    {service.pricing && (\n                      <div className=\"mb-6\">\n                        <div className=\"text-sm text-gray-500 mb-2\">Starting from</div>\n                        <div className=\"text-2xl font-bold text-brand-navy\">\n                          NPR {service.pricing.basic.toLocaleString()}\n                          <span className=\"text-sm font-normal text-gray-500\">/month</span>\n                        </div>\n                      </div>\n                    )}\n                    \n                    <Button \n                      asChild \n                      variant=\"outline\" \n                      className=\"w-full group-hover:bg-brand-navy group-hover:text-white group-hover:border-brand-navy transition-all duration-300\"\n                    >\n                      <Link href={`/services#${service.id}`}>\n                        Learn More\n                        <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                      </Link>\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n              Ready to Transform Your Digital Presence?\n            </h3>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Let&apos;s discuss how our comprehensive digital marketing solutions can help \n              your business reach new heights in the Nepali market.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                asChild \n                size=\"lg\" \n                className=\"bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold\"\n              >\n                <Link href=\"/contact\">\n                  Get Free Consultation\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n              </Button>\n              <Button \n                asChild \n                variant=\"outline\" \n                size=\"lg\" \n                className=\"border-white text-white hover:bg-white hover:text-brand-navy\"\n              >\n                <Link href=\"/services\">\n                  View All Services\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,UAAU;IACd,QAAA,0MAAA,CAAA,SAAM;IACN,QAAA,sMAAA,CAAA,SAAM;IACN,MAAA,kMAAA,CAAA,OAAI;IACJ,QAAA,sMAAA,CAAA,SAAM;IACN,SAAA,4MAAA,CAAA,UAAO;IACP,SAAA,wMAAA,CAAA,UAAO;AACT;AAEA,sDAAsD;AACtD,MAAM,kBAAkB,CAAC;IACvB,MAAM,gBAA2C;QAC/C,0BAA0B;QAC1B,oBAAoB;QACpB,mBAAmB;QACnB,cAAc;QACd,qBAAqB;QACrB,kBAAkB;IACpB;IACA,OAAO,aAAa,CAAC,MAAM,IAAI;AACjC;AAEe,SAAS;IACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEvD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAK;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;IAKlC;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAe;;;;;;;;;;;;;;;;IAIpC;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCAA4G;;;;;;sCAG3H,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,gBAAgB,OAAO,CAAC,QAAQ,IAAI,CAAyB,IAAI,0MAAA,CAAA,SAAM;wBAE7E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,gBAAgB,QAAQ,KAAK;gDAClC,KAAK,GAAG,QAAQ,KAAK,CAAC,QAAQ,CAAC;gDAC/B,IAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAc,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAK/B,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,QAAQ,KAAK;;;;;;0DAEhB,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,gBAAgB;;;;;;;;;;;;kDAG7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,6BAC1C,8OAAC;4DAAuB,WAAU;;8EAChC,8OAAC;oEAAI,WAAU;;;;;;gEACd;;2DAFO;;;;;oDAKX,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,8OAAC;wDAAI,WAAU;;4DAAsC;4DACjD,QAAQ,QAAQ,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;4CAKnC,QAAQ,OAAO,kBACd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,8OAAC;wDAAI,WAAU;;4DAAqC;4DAC7C,QAAQ,OAAO,CAAC,KAAK,CAAC,cAAc;0EACzC,8OAAC;gEAAK,WAAU;0EAAoC;;;;;;;;;;;;;;;;;;0DAK1D,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,SAAQ;gDACR,WAAU;0DAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;;wDAAE;sEAErC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/DzB,QAAQ,EAAE;;;;;oBAsErB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAW;8DAEpB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC", "debugId": null}}, {"offset": {"line": 2048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/packages.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { <PERSON>, Star, ArrowRight, Zap } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { usePackages } from '@/lib/queries/hooks';\nimport { Loading } from '@/components/ui/loading';\n\nexport default function Packages() {\n  const { data: packages, isLoading, error } = usePackages();\n\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center\">\n            <Loading size=\"lg\" text=\"Loading packages...\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error || !packages) {\n    return null;\n  }\n\n  // Show only the main packages (first 3)\n  const mainPackages = packages.slice(0, 3);\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            <Zap className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Pricing Packages\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Choose Your Growth\n            <span className=\"block text-brand-navy\">Package</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Transparent pricing with no hidden costs. Choose the package that fits your business \n            needs and budget. All packages include our signature Nepali market expertise.\n          </p>\n        </motion.div>\n\n        {/* Packages Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n          {mainPackages.map((pkg, index) => (\n            <motion.div\n              key={pkg.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <Card className={`h-full transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ${\n                pkg.popular \n                  ? 'border-2 border-brand-gold shadow-xl scale-105' \n                  : 'border border-gray-200 shadow-md'\n              }`}>\n                {pkg.popular && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                    <Badge className=\"bg-brand-gold text-gray-900 px-4 py-1 text-sm font-semibold\">\n                      <Star className=\"w-4 h-4 mr-1\" />\n                      Most Popular\n                    </Badge>\n                  </div>\n                )}\n\n                <CardHeader className=\"text-center pb-4\">\n                  <CardTitle className=\"text-2xl font-bold text-gray-900 mb-2\">\n                    {pkg.name}\n                  </CardTitle>\n                  <CardDescription className=\"text-gray-600 mb-6\">\n                    {pkg.description}\n                  </CardDescription>\n                  \n                  <div className=\"mb-6\">\n                    <div className=\"text-4xl font-bold text-brand-navy mb-2\">\n                      NPR {pkg.price.toLocaleString()}\n                      <span className=\"text-lg font-normal text-gray-500\">/{pkg.duration.toLowerCase()}</span>\n                    </div>\n                    {pkg.popular && (\n                      <div className=\"text-sm text-brand-gold font-medium\">\n                        Save 20% compared to individual services\n                      </div>\n                    )}\n                  </div>\n                </CardHeader>\n\n                <CardContent className=\"pt-0\">\n                  {/* Features List */}\n                  <div className=\"space-y-3 mb-8\">\n                    {pkg.features.map((feature, featureIndex) => (\n                      <div key={featureIndex} className=\"flex items-start\">\n                        <Check className=\"h-5 w-5 text-brand-gold mr-3 mt-0.5 flex-shrink-0\" />\n                        <span className=\"text-gray-700 text-sm leading-relaxed\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Ideal For */}\n                  <div className=\"mb-8\">\n                    <h4 className=\"text-sm font-semibold text-gray-900 mb-3\">Ideal for:</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {pkg.idealFor.map((ideal, idealIndex) => (\n                        <Badge \n                          key={idealIndex} \n                          variant=\"secondary\" \n                          className=\"text-xs bg-gray-100 text-gray-700\"\n                        >\n                          {ideal}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* CTA Button */}\n                  <Button \n                    asChild \n                    className={`w-full ${\n                      pkg.popular\n                        ? 'bg-brand-gold hover:bg-brand-gold-dark text-gray-900'\n                        : 'bg-brand-navy hover:bg-brand-navy-dark text-white'\n                    }`}\n                  >\n                    <Link href=\"/contact\">\n                      Get Started\n                      <ArrowRight className=\"ml-2 h-4 w-4\" />\n                    </Link>\n                  </Button>\n\n                  <div className=\"text-center mt-4\">\n                    <Link \n                      href=\"/packages\" \n                      className=\"text-sm text-brand-navy hover:underline\"\n                    >\n                      View detailed features\n                    </Link>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"bg-gray-50 rounded-3xl p-8 md:p-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Need a Custom Solution?\n            </h3>\n            <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Every business is unique. If our standard packages don&apos;t fit your specific needs, \n              we&apos;ll create a custom solution tailored to your goals and budget.\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n              {[\n                {\n                  title: 'Free Consultation',\n                  description: 'Discuss your goals and challenges with our experts'\n                },\n                {\n                  title: 'Custom Proposal',\n                  description: 'Receive a tailored strategy and pricing proposal'\n                },\n                {\n                  title: 'Flexible Terms',\n                  description: 'Choose payment terms that work for your business'\n                }\n              ].map((step, index) => (\n                <div key={step.title} className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-brand-navy text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\">\n                    {index + 1}\n                  </div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">{step.title}</h4>\n                  <p className=\"text-gray-600 text-sm\">{step.description}</p>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button asChild size=\"lg\" className=\"bg-brand-navy hover:bg-brand-navy-dark\">\n                <Link href=\"/contact\">\n                  Schedule Free Consultation\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n              </Button>\n              <Button asChild variant=\"outline\" size=\"lg\">\n                <Link href=\"/packages\">\n                  View All Packages\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEvD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAK;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;IAKlC;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,OAAO;IACT;IAEA,wCAAwC;IACxC,MAAM,eAAe,SAAS,KAAK,CAAC,GAAG;IAEvC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGlD,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAW,CAAC,yEAAyE,EACzF,IAAI,OAAO,GACP,mDACA,oCACJ;;oCACC,IAAI,OAAO,kBACV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMvC,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,IAAI,IAAI;;;;;;0DAEX,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,IAAI,WAAW;;;;;;0DAGlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAA0C;4DAClD,IAAI,KAAK,CAAC,cAAc;0EAC7B,8OAAC;gEAAK,WAAU;;oEAAoC;oEAAE,IAAI,QAAQ,CAAC,WAAW;;;;;;;;;;;;;oDAE/E,IAAI,OAAO,kBACV,8OAAC;wDAAI,WAAU;kEAAsC;;;;;;;;;;;;;;;;;;kDAO3D,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;0DACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC1B,8OAAC;wDAAuB,WAAU;;0EAChC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAK,WAAU;0EAAyC;;;;;;;uDAFjD;;;;;;;;;;0DAQd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAI,WAAU;kEACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,2BACxB,8OAAC,iIAAA,CAAA,QAAK;gEAEJ,SAAQ;gEACR,WAAU;0EAET;+DAJI;;;;;;;;;;;;;;;;0DAWb,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,WAAW,CAAC,OAAO,EACjB,IAAI,OAAO,GACP,yDACA,qDACJ;0DAEF,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;wDAAW;sEAEpB,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;2BAxFF,IAAI,EAAE;;;;;;;;;;8BAmGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;0DACZ,QAAQ;;;;;;0DAEX,8OAAC;gDAAG,WAAU;0DAA4C,KAAK,KAAK;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,WAAW;;;;;;;uCAL9C,KAAK,KAAK;;;;;;;;;;0CAUxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,WAAU;kDAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAW;8DAEpB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;kDACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC", "debugId": null}}, {"offset": {"line": 2633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/portfolio-showcase.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ArrowRight, ExternalLink, TrendingUp, Users, Award } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { usePortfolio } from '@/lib/queries/hooks';\nimport { Loading } from '@/components/ui/loading';\n\n// Helper function to get portfolio project images from Unsplash\nconst getPortfolioImage = (category: string, index: number) => {\n  const imagesByCategory: { [key: string]: string[] } = {\n    'E-commerce': [\n      'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n      'https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    ],\n    'Restaurant': [\n      'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n      'https://images.unsplash.com/photo-**********-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    ],\n    'Technology': [\n      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    ],\n    'Healthcare': [\n      'https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    ],\n    'default': [\n      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',\n    ]\n  };\n\n  const categoryImages = imagesByCategory[category] || imagesByCategory['default'];\n  return categoryImages[index % categoryImages.length];\n};\n\nexport default function PortfolioShowcase() {\n  const { data: portfolio, isLoading, error } = usePortfolio();\n\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center\">\n            <Loading size=\"lg\" text=\"Loading portfolio...\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error || !portfolio) {\n    return null;\n  }\n\n  // Show only featured portfolio items (first 3)\n  const featuredPortfolio = portfolio.slice(0, 3);\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            <Award className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Success Stories\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            Real Results for\n            <span className=\"block text-brand-navy\">Real Businesses</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            See how we&apos;ve helped Nepali businesses transform their digital presence and achieve \n            remarkable growth through strategic digital marketing campaigns.\n          </p>\n        </motion.div>\n\n        {/* Portfolio Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16\">\n          {featuredPortfolio.map((item, index) => (\n            <motion.div\n              key={item.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <Card className=\"h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2\">\n                {/* Project Image */}\n                <div className=\"relative h-48 overflow-hidden\">\n                  <Image\n                    src={getPortfolioImage(item.category, index)}\n                    alt={`${item.client} - ${item.category} project`}\n                    fill\n                    className=\"object-cover group-hover:scale-110 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <Badge className=\"bg-brand-gold text-gray-900 mb-2\">\n                      {item.category}\n                    </Badge>\n                    <h3 className=\"text-lg font-bold\">{item.client}</h3>\n                  </div>\n\n                  {/* Hover Overlay */}\n                  <div className=\"absolute inset-0 bg-brand-gold/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                    <div className=\"text-center text-gray-900\">\n                      <ExternalLink className=\"h-8 w-8 mx-auto mb-2\" />\n                      <span className=\"font-semibold\">View Case Study</span>\n                    </div>\n                  </div>\n                </div>\n\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-brand-navy transition-colors\">\n                    {item.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {item.description}\n                  </p>\n\n                  {/* Results */}\n                  <div className=\"space-y-4 mb-6\">\n                    {item.results.slice(0, 2).map((result, resultIndex) => (\n                      <div key={resultIndex} className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-600\">{result.metric}</span>\n                        <div className=\"text-right\">\n                          <div className=\"text-lg font-bold text-brand-navy\">{result.value}</div>\n                          <div className=\"text-xs text-brand-gold\">{result.improvement}</div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Technologies */}\n                  <div className=\"mb-6\">\n                    <div className=\"flex flex-wrap gap-2\">\n                      {item.technologies.slice(0, 3).map((tech, techIndex) => (\n                        <Badge key={techIndex} variant=\"secondary\" className=\"text-xs\">\n                          {tech}\n                        </Badge>\n                      ))}\n                      {item.technologies.length > 3 && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          +{item.technologies.length - 3} more\n                        </Badge>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Duration */}\n                  <div className=\"text-sm text-gray-500 mb-4\">\n                    Project Duration: {item.duration}\n                  </div>\n\n                  {/* CTA */}\n                  <Button \n                    asChild \n                    variant=\"outline\" \n                    className=\"w-full group-hover:bg-brand-navy group-hover:text-white group-hover:border-brand-navy transition-all duration-300\"\n                  >\n                    <Link href={`/portfolio/${item.id}`}>\n                      View Case Study\n                      <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-white rounded-3xl p-8 md:p-12 mb-16\"\n        >\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Portfolio Highlights\n            </h3>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Our portfolio speaks for itself. Here are some key metrics from our successful projects.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            {[\n              { icon: TrendingUp, value: '500%', label: 'Average Traffic Increase' },\n              { icon: Users, value: '300%', label: 'Social Media Growth' },\n              { icon: Award, value: '250%', label: 'Lead Generation Boost' },\n              { icon: ExternalLink, value: '98%', label: 'Client Satisfaction' }\n            ].map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center\"\n              >\n                <div className=\"w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <stat.icon className=\"h-8 w-8 text-white\" />\n                </div>\n                <div className=\"text-3xl font-bold text-brand-navy mb-2\">{stat.value}</div>\n                <div className=\"text-sm text-gray-600\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n              Ready to Be Our Next Success Story?\n            </h3>\n            <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Join the ranks of successful Nepali businesses who have transformed their \n              digital presence with our proven strategies and expert guidance.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                asChild \n                size=\"lg\" \n                className=\"bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold\"\n              >\n                <Link href=\"/contact\">\n                  Start Your Project\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n              </Button>\n              <Button \n                asChild \n                variant=\"outline\" \n                size=\"lg\" \n                className=\"border-white text-white hover:bg-white hover:text-brand-navy\"\n              >\n                <Link href=\"/portfolio\">\n                  View Full Portfolio\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,gEAAgE;AAChE,MAAM,oBAAoB,CAAC,UAAkB;IAC3C,MAAM,mBAAgD;QACpD,cAAc;YACZ;YACA;SACD;QACD,cAAc;YACZ;YACA;SACD;QACD,cAAc;YACZ;YACA;SACD;QACD,cAAc;YACZ;SACD;QACD,WAAW;YACT;SACD;IACH;IAEA,MAAM,iBAAiB,gBAAgB,CAAC,SAAS,IAAI,gBAAgB,CAAC,UAAU;IAChF,OAAO,cAAc,CAAC,QAAQ,eAAe,MAAM,CAAC;AACtD;AAEe,SAAS;IACtB,MAAM,EAAE,MAAM,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAEzD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAK;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;IAKlC;IAEA,IAAI,SAAS,CAAC,WAAW;QACvB,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,oBAAoB,UAAU,KAAK,CAAC,GAAG;IAE7C,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGpD,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,kBAAkB,KAAK,QAAQ,EAAE;gDACtC,KAAK,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,QAAQ,CAAC,QAAQ,CAAC;gDAChD,IAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEACd,KAAK,QAAQ;;;;;;kEAEhB,8OAAC;wDAAG,WAAU;kEAAqB,KAAK,MAAM;;;;;;;;;;;;0DAIhD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;kDAKtC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAInB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,4BACrC,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAK,WAAU;0EAAyB,OAAO,MAAM;;;;;;0EACtD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAqC,OAAO,KAAK;;;;;;kFAChE,8OAAC;wEAAI,WAAU;kFAA2B,OAAO,WAAW;;;;;;;;;;;;;uDAJtD;;;;;;;;;;0DAWd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,0BACxC,8OAAC,iIAAA,CAAA,QAAK;gEAAiB,SAAQ;gEAAY,WAAU;0EAClD;+DADS;;;;;wDAIb,KAAK,YAAY,CAAC,MAAM,GAAG,mBAC1B,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAAU;gEAC3C,KAAK,YAAY,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;0DAOvC,8OAAC;gDAAI,WAAU;;oDAA6B;oDACvB,KAAK,QAAQ;;;;;;;0DAIlC,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,SAAQ;gDACR,WAAU;0DAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;;wDAAE;sEAEnC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnFzB,KAAK,EAAE;;;;;;;;;;8BA6FlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM,kNAAA,CAAA,aAAU;oCAAE,OAAO;oCAAQ,OAAO;gCAA2B;gCACrE;oCAAE,MAAM,oMAAA,CAAA,QAAK;oCAAE,OAAO;oCAAQ,OAAO;gCAAsB;gCAC3D;oCAAE,MAAM,oMAAA,CAAA,QAAK;oCAAE,OAAO;oCAAQ,OAAO;gCAAwB;gCAC7D;oCAAE,MAAM,sNAAA,CAAA,eAAY;oCAAE,OAAO;oCAAO,OAAO;gCAAsB;6BAClE,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAI,WAAU;sDAA2C,KAAK,KAAK;;;;;;sDACpE,8OAAC;4CAAI,WAAU;sDAAyB,KAAK,KAAK;;;;;;;mCAX7C,KAAK,KAAK;;;;;;;;;;;;;;;;8BAkBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAW;8DAEpB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxC", "debugId": null}}, {"offset": {"line": 3309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { <PERSON>, Quote, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { useTestimonials } from '@/lib/queries/hooks';\nimport { Loading } from '@/components/ui/loading';\nimport { useState, useEffect } from 'react';\n\n// Helper function to get client photos from Unsplash\nconst getClientPhoto = (name: string, index: number) => {\n  const photos = [\n    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',\n    'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',\n    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',\n    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',\n    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',\n    'https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80',\n  ];\n  return photos[index % photos.length];\n};\n\nexport default function Testimonials() {\n  const { data: testimonials, isLoading, error } = useTestimonials();\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying || !testimonials || testimonials.length <= 1) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, testimonials]);\n\n  const nextTestimonial = () => {\n    if (!testimonials) return;\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n    setIsAutoPlaying(false);\n  };\n\n  const prevTestimonial = () => {\n    if (!testimonials) return;\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n    setIsAutoPlaying(false);\n  };\n\n  const goToTestimonial = (index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n  };\n\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center\">\n            <Loading size=\"lg\" text=\"Loading testimonials...\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error || !testimonials || testimonials.length === 0) {\n    return null;\n  }\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6\">\n            <Star className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Client Success Stories\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            What Our Clients Say\n            <span className=\"block text-brand-navy\">About Us</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Don&apos;t just take our word for it. Here&apos;s what Nepali business owners have to say \n            about their experience working with Lunar Cubes.\n          </p>\n        </motion.div>\n\n        {/* Main Testimonial */}\n        <motion.div\n          key={currentIndex}\n          initial={{ opacity: 0, x: 50 }}\n          animate={{ opacity: 1, x: 0 }}\n          exit={{ opacity: 0, x: -50 }}\n          transition={{ duration: 0.6 }}\n          className=\"max-w-4xl mx-auto mb-12\"\n        >\n          <Card className=\"border-0 shadow-xl bg-white\">\n            <CardContent className=\"p-8 md:p-12\">\n              <div className=\"flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8\">\n                {/* Client Image */}\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-24 h-24 md:w-32 md:h-32 rounded-full bg-gradient-to-br from-brand-navy to-brand-gold p-1\">\n                    <div className=\"w-full h-full rounded-full overflow-hidden\">\n                      <Image\n                        src={getClientPhoto(currentTestimonial.name, currentIndex)}\n                        alt={`${currentTestimonial.name} - ${currentTestimonial.company}`}\n                        width={128}\n                        height={128}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Testimonial Content */}\n                <div className=\"flex-1 text-center md:text-left\">\n                  {/* Quote Icon */}\n                  <Quote className=\"h-8 w-8 text-brand-gold mb-4 mx-auto md:mx-0\" />\n                  \n                  {/* Rating */}\n                  <div className=\"flex justify-center md:justify-start space-x-1 mb-4\">\n                    {[...Array(5)].map((_, i) => (\n                      <Star\n                        key={i}\n                        className={`h-5 w-5 ${\n                          i < currentTestimonial.rating\n                            ? 'text-brand-gold fill-current'\n                            : 'text-gray-300'\n                        }`}\n                      />\n                    ))}\n                  </div>\n\n                  {/* Testimonial Text */}\n                  <blockquote className=\"text-lg md:text-xl text-gray-700 leading-relaxed mb-6 italic\">\n                    &quot;{currentTestimonial.content}&quot;\n                  </blockquote>\n\n                  {/* Client Info */}\n                  <div>\n                    <div className=\"text-xl font-bold text-gray-900 mb-1\">\n                      {currentTestimonial.name}\n                    </div>\n                    <div className=\"text-brand-navy font-medium mb-1\">\n                      {currentTestimonial.position}\n                    </div>\n                    <div className=\"text-gray-600 mb-2\">\n                      {currentTestimonial.company}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      {currentTestimonial.location} • {currentTestimonial.serviceUsed}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n\n        {/* Navigation */}\n        <div className=\"flex items-center justify-center space-x-4 mb-8\">\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            onClick={prevTestimonial}\n            className=\"rounded-full border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white\"\n          >\n            <ChevronLeft className=\"h-4 w-4\" />\n          </Button>\n\n          {/* Dots Indicator */}\n          <div className=\"flex space-x-2\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToTestimonial(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentIndex\n                    ? 'bg-brand-navy scale-125'\n                    : 'bg-gray-300 hover:bg-gray-400'\n                }`}\n                aria-label={`Go to testimonial ${index + 1}`}\n              />\n            ))}\n          </div>\n\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            onClick={nextTestimonial}\n            className=\"rounded-full border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white\"\n          >\n            <ChevronRight className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* Additional Testimonials Preview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          {testimonials\n            .filter((_, index) => index !== currentIndex)\n            .slice(0, 3)\n            .map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"cursor-pointer\"\n                onClick={() => goToTestimonial(testimonials.findIndex(t => t.id === testimonial.id))}\n              >\n                <Card className=\"h-full hover:shadow-lg transition-shadow duration-300 border border-gray-200\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex space-x-1 mb-3\">\n                      {[...Array(5)].map((_, i) => (\n                        <Star\n                          key={i}\n                          className={`h-4 w-4 ${\n                            i < testimonial.rating\n                              ? 'text-brand-gold fill-current'\n                              : 'text-gray-300'\n                          }`}\n                        />\n                      ))}\n                    </div>\n                    <p className=\"text-gray-700 text-sm mb-4 line-clamp-3\">\n                      &quot;{testimonial.content}&quot;\n                    </p>\n                    <div>\n                      <div className=\"font-semibold text-gray-900 text-sm\">\n                        {testimonial.name}\n                      </div>\n                      <div className=\"text-xs text-gray-600\">\n                        {testimonial.position}, {testimonial.company}\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n        </div>\n\n        {/* Auto-play indicator */}\n        {isAutoPlaying && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-center mt-6\"\n          >\n            <div className=\"text-sm text-gray-500\">\n              Auto-playing testimonials • Click navigation to pause\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,qDAAqD;AACrD,MAAM,iBAAiB,CAAC,MAAc;IACpC,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;AACtC;AAEe,SAAS;IACtB,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,aAAa,MAAM,IAAI,GAAG;QAEjE,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAC5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe;KAAa;IAEhC,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc;QACnB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAC1D,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc;QACnB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;QAChF,iBAAiB;IACnB;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAK;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;IAKlC;IAEA,IAAI,SAAS,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;QACvD,OAAO;IACT;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGnD,8OAAC;4BAAG,WAAU;;gCAAgE;8CAE5E,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,eAAe,mBAAmB,IAAI,EAAE;oDAC7C,KAAK,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,mBAAmB,OAAO,EAAE;oDACjE,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,mBAAmB,MAAM,GACzB,iCACA,iBACJ;uDALG;;;;;;;;;;0DAWX,8OAAC;gDAAW,WAAU;;oDAA+D;oDAC5E,mBAAmB,OAAO;oDAAC;;;;;;;0DAIpC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,mBAAmB,IAAI;;;;;;kEAE1B,8OAAC;wDAAI,WAAU;kEACZ,mBAAmB,QAAQ;;;;;;kEAE9B,8OAAC;wDAAI,WAAU;kEACZ,mBAAmB,OAAO;;;;;;kEAE7B,8OAAC;wDAAI,WAAU;;4DACZ,mBAAmB,QAAQ;4DAAC;4DAAI,mBAAmB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA7DtE;;;;;8BAuEP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,4BACA,iCACJ;oCACF,cAAY,CAAC,kBAAkB,EAAE,QAAQ,GAAG;mCAPvC;;;;;;;;;;sCAYX,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAI,WAAU;8BACZ,aACE,MAAM,CAAC,CAAC,GAAG,QAAU,UAAU,cAC/B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,aAAa,sBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;4BACV,SAAS,IAAM,gBAAgB,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;sCAElF,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,YAAY,MAAM,GAClB,iCACA,iBACJ;mDALG;;;;;;;;;;sDASX,8OAAC;4CAAE,WAAU;;gDAA0C;gDAC9C,YAAY,OAAO;gDAAC;;;;;;;sDAE7B,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACZ,YAAY,IAAI;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;;wDACZ,YAAY,QAAQ;wDAAC;wDAAG,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;2BA9B/C,YAAY,EAAE;;;;;;;;;;gBAwC1B,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 3844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 4090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,qMAAA,CAAA,gBAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,qMAAA,CAAA,aAAgB,CAAC;IACtC,MAAM,cAAc,qMAAA,CAAA,aAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,qMAAA,CAAA,gBAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,qMAAA,CAAA,QAAW;IAEtB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 4266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/lead-capture.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Rocket, CheckCircle, Phone, Mail, MessageSquare } from 'lucide-react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\n\nconst leadFormSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phone: z.string().optional(),\n  company: z.string().optional(),\n  service: z.string().min(1, 'Please select a service'),\n  message: z.string().min(10, 'Message must be at least 10 characters'),\n});\n\ntype LeadFormData = z.infer<typeof leadFormSchema>;\n\nconst services = [\n  'Social Media Marketing',\n  'Search Engine Optimization (SEO)',\n  'Web Development & Design',\n  'Google Ads Management',\n  'Content Marketing',\n  'Brand Identity & Design',\n  'Complete Digital Marketing Package',\n  'Custom Solution'\n];\n\nexport default function LeadCapture() {\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const form = useForm<LeadFormData>({\n    resolver: zodResolver(leadFormSchema),\n    defaultValues: {\n      name: '',\n      email: '',\n      phone: '',\n      company: '',\n      service: '',\n      message: '',\n    },\n  });\n\n  const onSubmit = async (data: LeadFormData) => {\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    console.log('Lead form submitted:', data);\n    setIsSubmitted(true);\n    setIsSubmitting(false);\n    form.reset();\n  };\n\n  if (isSubmitted) {\n    return (\n      <section className=\"py-20 bg-gradient-to-br from-brand-navy to-brand-navy-dark\">\n        <div className=\"container mx-auto px-4\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n            className=\"max-w-2xl mx-auto text-center text-white\"\n          >\n            <div className=\"w-20 h-20 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-6\">\n              <CheckCircle className=\"h-10 w-10 text-gray-900\" />\n            </div>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Thank You for Your Interest!\n            </h2>\n            <p className=\"text-blue-100 mb-8 text-lg\">\n              We&apos;ve received your message and will get back to you within 24 hours with a \n              customized proposal for your digital marketing needs.\n            </p>\n            <Button \n              onClick={() => setIsSubmitted(false)}\n              className=\"bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold\"\n            >\n              Submit Another Inquiry\n            </Button>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-brand-navy to-brand-navy-dark\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-white\"\n          >\n            <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/20 text-brand-gold rounded-full text-sm font-medium mb-6\">\n              <Rocket className=\"w-4 h-4 mr-2\" />\n              Ready to Launch Your Success?\n            </div>\n            \n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6\">\n              Get Your Free Digital Marketing\n              <span className=\"block text-brand-gold\">Strategy Session</span>\n            </h2>\n            \n            <p className=\"text-blue-100 text-lg mb-8 leading-relaxed\">\n              Take the first step towards digital success. Our experts will analyze your current \n              digital presence and create a customized strategy to help your business grow in the Nepali market.\n            </p>\n\n            <div className=\"space-y-6 mb-8\">\n              {[\n                {\n                  icon: CheckCircle,\n                  title: 'Free 30-minute consultation',\n                  description: 'Discuss your goals and challenges with our experts'\n                },\n                {\n                  icon: CheckCircle,\n                  title: 'Customized strategy proposal',\n                  description: 'Receive a tailored plan for your business'\n                },\n                {\n                  icon: CheckCircle,\n                  title: 'No obligation commitment',\n                  description: 'Learn about opportunities with no pressure'\n                }\n              ].map((benefit, index) => (\n                <motion.div\n                  key={benefit.title}\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-start space-x-4\"\n                >\n                  <benefit.icon className=\"h-6 w-6 text-brand-gold mt-1 flex-shrink-0\" />\n                  <div>\n                    <h4 className=\"font-semibold mb-1\">{benefit.title}</h4>\n                    <p className=\"text-blue-100 text-sm\">{benefit.description}</p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3\">\n                <Phone className=\"h-5 w-5 text-brand-gold\" />\n                <span className=\"text-blue-100\">+977-1-4441234</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Mail className=\"h-5 w-5 text-brand-gold\" />\n                <span className=\"text-blue-100\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <MessageSquare className=\"h-5 w-5 text-brand-gold\" />\n                <span className=\"text-blue-100\">WhatsApp: +977-9841234567</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Right Content - Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"shadow-2xl border-0\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-gray-900\">\n                  Start Your Digital Journey\n                </CardTitle>\n                <CardDescription>\n                  Fill out the form below and we&apos;ll get back to you within 24 hours with a \n                  customized proposal for your business.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Form {...form}>\n                  <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <FormField\n                        control={form.control}\n                        name=\"name\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>Full Name *</FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"Your full name\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                      \n                      <FormField\n                        control={form.control}\n                        name=\"email\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>Email Address *</FormLabel>\n                            <FormControl>\n                              <Input type=\"email\" placeholder=\"<EMAIL>\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <FormField\n                        control={form.control}\n                        name=\"phone\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>Phone Number</FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"+977-98xxxxxxxx\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                      \n                      <FormField\n                        control={form.control}\n                        name=\"company\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>Company Name</FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"Your company name\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                    </div>\n\n                    <FormField\n                      control={form.control}\n                      name=\"service\"\n                      render={({ field }) => (\n                        <FormItem>\n                          <FormLabel>Service of Interest *</FormLabel>\n                          <Select onValueChange={field.onChange} defaultValue={field.value}>\n                            <FormControl>\n                              <SelectTrigger>\n                                <SelectValue placeholder=\"Select a service\" />\n                              </SelectTrigger>\n                            </FormControl>\n                            <SelectContent>\n                              {services.map((service) => (\n                                <SelectItem key={service} value={service}>\n                                  {service}\n                                </SelectItem>\n                              ))}\n                            </SelectContent>\n                          </Select>\n                          <FormMessage />\n                        </FormItem>\n                      )}\n                    />\n\n                    <FormField\n                      control={form.control}\n                      name=\"message\"\n                      render={({ field }) => (\n                        <FormItem>\n                          <FormLabel>Tell us about your project *</FormLabel>\n                          <FormControl>\n                            <Textarea \n                              placeholder=\"Describe your business goals, current challenges, and what you hope to achieve with digital marketing...\"\n                              className=\"min-h-[120px]\"\n                              {...field}\n                            />\n                          </FormControl>\n                          <FormMessage />\n                        </FormItem>\n                      )}\n                    />\n\n                    <Button \n                      type=\"submit\" \n                      className=\"w-full bg-brand-navy hover:bg-brand-navy-dark text-white py-3 text-lg font-semibold\"\n                      disabled={isSubmitting}\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                          Sending...\n                        </>\n                      ) : (\n                        <>\n                          Get My Free Strategy Session\n                          <Rocket className=\"ml-2 h-5 w-5\" />\n                        </>\n                      )}\n                    </Button>\n\n                    <p className=\"text-xs text-gray-500 text-center\">\n                      By submitting this form, you agree to receive marketing communications from Lunar Cubes. \n                      You can unsubscribe at any time.\n                    </p>\n                  </form>\n                </Form>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeA,MAAM,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;AAC9B;AAIA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAgB;QACjC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,QAAQ,GAAG,CAAC,wBAAwB;QACpC,eAAe;QACf,gBAAgB;QAChB,KAAK,KAAK;IACZ;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIrC,8OAAC;gCAAG,WAAU;;oCAAkD;kDAE9D,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAG1C,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAK1D,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM,2NAAA,CAAA,cAAW;wCACjB,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM,2NAAA,CAAA,cAAW;wCACjB,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM,2NAAA,CAAA,cAAW;wCACjB,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;0DACxB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsB,QAAQ,KAAK;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;;;;;;;;uCAVtD,QAAQ,KAAK;;;;;;;;;;0CAiBxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAmC;;;;;;sDAGxD,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAKnB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAE,GAAG,IAAI;kDACZ,cAAA,8OAAC;4CAAK,UAAU,KAAK,YAAY,CAAC;4CAAW,WAAU;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sFACP,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gFAAC,aAAY;gFAAkB,GAAG,KAAK;;;;;;;;;;;sFAE/C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sEAKlB,8OAAC,gIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sFACP,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gFAAC,MAAK;gFAAQ,aAAY;gFAAkB,GAAG,KAAK;;;;;;;;;;;sFAE5D,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8DAMpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sFACP,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gFAAC,aAAY;gFAAmB,GAAG,KAAK;;;;;;;;;;;sFAEhD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sEAKlB,8OAAC,gIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sFACP,8OAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gFAAC,aAAY;gFAAqB,GAAG,KAAK;;;;;;;;;;;sFAElD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8DAMpB,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,kIAAA,CAAA,SAAM;oEAAC,eAAe,MAAM,QAAQ;oEAAE,cAAc,MAAM,KAAK;;sFAC9D,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,8OAAC,kIAAA,CAAA,gBAAa;sFACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,kIAAA,CAAA,aAAU;oFAAe,OAAO;8FAC9B;mFADc;;;;;;;;;;;;;;;;8EAMvB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wEACP,aAAY;wEACZ,WAAU;wEACT,GAAG,KAAK;;;;;;;;;;;8EAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8DAKlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,UAAU;8DAET,6BACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;4DAAE;0EAEA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;8DAKxB,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarE", "debugId": null}}]}