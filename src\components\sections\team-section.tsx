'use client';

import { motion } from 'framer-motion';
import { Linkedin, Mail, Star, Users } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTeamMembers } from '@/lib/queries/hooks';
import { Loading } from '@/components/ui/loading';

export default function TeamSection() {
  const { data: team, isLoading, error } = useTeamMembers();

  if (isLoading) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Loading size="lg" text="Loading team members..." />
          </div>
        </div>
      </section>
    );
  }

  if (error || !team) {
    return null;
  }

  return (
    <section className="py-20 bg-gray-50" id="team">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6">
            <Users className="w-4 h-4 mr-2 text-brand-gold" />
            Meet Our Team
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            The Experts Behind
            <span className="block text-brand-navy">Your Success</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our diverse team of digital marketing professionals brings together years of experience, 
            creative thinking, and deep understanding of the Nepali market to deliver exceptional results.
          </p>
        </motion.div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {team.map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Card className="h-full border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 overflow-hidden">
                {/* Profile Image */}
                <div className="relative h-64 bg-gradient-to-br from-brand-navy to-brand-gold overflow-hidden">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-32 h-32 bg-white rounded-full flex items-center justify-center text-4xl font-bold text-brand-navy">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </div>
                  </div>
                  
                  {/* Social Links Overlay */}
                  <div className="absolute inset-0 bg-brand-navy/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-4">
                    {member.social?.linkedin && (
                      <a
                        href={member.social.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-brand-navy hover:bg-brand-gold hover:text-gray-900 transition-colors"
                      >
                        <Linkedin className="h-5 w-5" />
                      </a>
                    )}
                    {member.social?.email && (
                      <a
                        href={`mailto:${member.social.email}`}
                        className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-brand-navy hover:bg-brand-gold hover:text-gray-900 transition-colors"
                      >
                        <Mail className="h-5 w-5" />
                      </a>
                    )}
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {member.name}
                    </h3>
                    <p className="text-brand-navy font-semibold mb-2">
                      {member.position}
                    </p>
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                      <Star className="h-4 w-4 text-brand-gold" />
                      <span>{member.experience} experience</span>
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {member.bio}
                  </p>

                  {/* Expertise Tags */}
                  <div className="mb-4">
                    <div className="text-sm font-semibold text-gray-900 mb-2">Expertise:</div>
                    <div className="flex flex-wrap gap-2">
                      {member.expertise.map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Education */}
                  {member.education && (
                    <div className="text-xs text-gray-500 border-t pt-3">
                      <strong>Education:</strong> {member.education}
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Team Culture Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Our Team Culture
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We believe that great results come from great people working together with shared values and vision.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: 'Collaboration',
                description: 'We work together as one team, sharing knowledge and supporting each other to achieve the best results for our clients.',
                icon: '🤝'
              },
              {
                title: 'Innovation',
                description: 'We stay ahead of digital marketing trends and continuously learn new strategies to keep our clients competitive.',
                icon: '💡'
              },
              {
                title: 'Integrity',
                description: 'We believe in honest communication, transparent reporting, and always doing what\'s best for our clients\' success.',
                icon: '🎯'
              }
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h4>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Join Team CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Want to Join Our Team?
            </h3>
            <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
              We&apos;re always looking for talented individuals who are passionate about digital marketing 
              and want to make a difference in Nepal&apos;s business landscape.
            </p>
            <Button 
              asChild 
              size="lg" 
              className="bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold"
            >
              <a href="mailto:<EMAIL>">
                View Open Positions
              </a>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
