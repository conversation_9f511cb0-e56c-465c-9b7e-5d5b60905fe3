import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import BlogPostHeader from '@/components/sections/blog-post-header';
import BlogPostContent from '@/components/sections/blog-post-content';
import BlogPostSidebar from '@/components/sections/blog-post-sidebar';
import BlogPostRelated from '@/components/sections/blog-post-related';
import BlogNewsletter from '@/components/sections/blog-newsletter';

// Server component for better SEO
async function getBlogPost(slug: string) {
  try {
    // In development, read from file system directly
    if (process.env.NODE_ENV === 'development') {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/blog-posts.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const posts = JSON.parse(fileContents);
      return posts.find((post: any) => post.slug === slug);
    }

    // In production, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/blog-posts.json`, {
      cache: 'force-cache',
    });
    if (!response.ok) throw new Error('Failed to fetch blog posts');
    const posts = await response.json();
    return posts.find((post: any) => post.slug === slug);
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
}

async function getAllBlogPosts() {
  try {
    // In development, read from file system directly
    if (process.env.NODE_ENV === 'development') {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/blog-posts.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(fileContents);
    }

    // In production, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/blog-posts.json`, {
      cache: 'force-cache',
    });
    if (!response.ok) throw new Error('Failed to fetch blog posts');
    return response.json();
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return [];
  }
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  const posts = await getAllBlogPosts();
  return posts.map((post: any) => ({
    slug: post.slug,
  }));
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const post = await getBlogPost(params.slug);

  if (!post) {
    return {
      title: 'Blog Post Not Found | Lunar Cubes',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: post.seo.metaTitle,
    description: post.seo.metaDescription,
    keywords: post.seo.keywords.join(', '),
    authors: [{ name: post.author.name }],
    openGraph: {
      title: post.seo.metaTitle,
      description: post.seo.metaDescription,
      url: `/blog/${post.slug}`,
      type: 'article',
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt,
      authors: [post.author.name],
      images: [
        {
          url: post.featuredImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      tags: post.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seo.metaTitle,
      description: post.seo.metaDescription,
      images: [post.featuredImage],
      creator: `@${post.author.name.replace(' ', '').toLowerCase()}`,
    },
    alternates: {
      canonical: post.seo.canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const [post, allPosts] = await Promise.all([
    getBlogPost(params.slug),
    getAllBlogPosts(),
  ]);

  if (!post) {
    notFound();
  }

  // Get related posts (same category, excluding current post)
  const relatedPosts = allPosts
    .filter((p: any) => p.id !== post.id && p.category.slug === post.category.slug)
    .slice(0, 3);

  // Structured data for SEO
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: post.featuredImage,
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog/${post.slug}`,
    datePublished: post.publishedAt,
    dateModified: post.updatedAt,
    author: {
      '@type': 'Person',
      name: post.author.name,
      image: post.author.avatar,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Lunar Cubes',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/images/logo.svg`,
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog/${post.slug}`,
    },
    keywords: post.tags.join(', '),
    articleSection: post.category.name,
    wordCount: post.content.split(' ').length,
    timeRequired: `PT${post.readingTime}M`,
  };

  // Breadcrumb structured data
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Blog',
        item: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog`,
      },
      {
        '@type': 'ListItem',
        position: 3,
        name: post.category.name,
        item: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog?category=${post.category.slug}`,
      },
      {
        '@type': 'ListItem',
        position: 4,
        name: post.title,
        item: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog/${post.slug}`,
      },
    ],
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbData) }}
      />
      
      <div>
        <BlogPostHeader post={post} />
        
        <div className="container mx-auto px-4 py-12">
          <div className="grid lg:grid-cols-4 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <BlogPostContent post={post} />
            </div>
            
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <BlogPostSidebar post={post} allPosts={allPosts} />
            </div>
          </div>
        </div>
        
        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <BlogPostRelated posts={relatedPosts} currentPost={post} />
        )}
        
        {/* Newsletter */}
        <BlogNewsletter />
      </div>
    </>
  );
}
