'use client';

import { motion } from 'framer-motion';
import { MapPin, Calendar, Users, Award } from 'lucide-react';
import { useCompanyInfo } from '@/lib/queries/hooks';

export default function AboutHero() {
  const { data: company } = useCompanyInfo();

  return (
    <section className="relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6"
          >
            <Award className="w-4 h-4 mr-2 text-brand-gold" />
            About Lunar Cubes
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Transforming Nepal&apos;s Digital
            <span className="block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent">
              Marketing Landscape
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            We are Nepal&apos;s premier digital marketing agency, dedicated to empowering small and 
            medium businesses with innovative strategies that drive growth and create lasting success 
            in the digital age.
          </motion.p>

          {/* Company Stats */}
          {company && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
            >
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <Calendar className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-3xl font-bold text-brand-navy mb-2">
                  {company.founded}
                </div>
                <div className="text-gray-600">Founded</div>
              </div>
              
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <Users className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-3xl font-bold text-brand-navy mb-2">
                  {company.stats.clientsServed}+
                </div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <Award className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-3xl font-bold text-brand-navy mb-2">
                  {company.stats.projectsCompleted}+
                </div>
                <div className="text-gray-600">Projects</div>
              </div>
              
              <div className="text-center">
                <div className="flex justify-center mb-3">
                  <MapPin className="h-8 w-8 text-brand-gold" />
                </div>
                <div className="text-3xl font-bold text-brand-navy mb-2">
                  {company.stats.teamMembers}+
                </div>
                <div className="text-gray-600">Team Members</div>
              </div>
            </motion.div>
          )}

          {/* Mission Statement */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100"
          >
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
              Our Mission
            </h2>
            {company && (
              <p className="text-lg text-gray-700 leading-relaxed">
                {company.mission}
              </p>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
