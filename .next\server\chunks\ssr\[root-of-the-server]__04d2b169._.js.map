{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-list.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-list.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-list.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-list.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-list.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-list.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-categories.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-categories.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-categories.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-categories.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-categories.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-categories.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-newsletter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-newsletter.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-newsletter.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-newsletter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/blog-newsletter.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/blog-newsletter.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/app/blog/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport BlogHero from '@/components/sections/blog-hero';\nimport BlogList from '@/components/sections/blog-list';\nimport BlogCategories from '@/components/sections/blog-categories';\nimport BlogNewsletter from '@/components/sections/blog-newsletter';\n\n// Server component for better SEO\nexport const metadata: Metadata = {\n  title: 'Digital Marketing Blog | Expert Insights & Tips | Lunar Cubes Nepal',\n  description: 'Stay updated with the latest digital marketing trends, SEO tips, social media strategies, and expert insights for Nepali businesses. Read our comprehensive blog.',\n  keywords: [\n    'digital marketing blog Nepal',\n    'SEO tips Nepal',\n    'social media marketing blog',\n    'content marketing Nepal',\n    'online marketing insights',\n    'digital marketing trends Nepal',\n    'marketing blog Kathmandu',\n    'business growth tips Nepal'\n  ].join(', '),\n  openGraph: {\n    title: 'Digital Marketing Blog | Expert Insights & Tips | Lunar Cubes',\n    description: 'Expert digital marketing insights, SEO tips, and growth strategies for Nepali businesses. Stay ahead with our comprehensive blog.',\n    url: '/blog',\n    type: 'website',\n    images: [\n      {\n        url: 'https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',\n        width: 1200,\n        height: 630,\n        alt: 'Digital Marketing Blog - Lunar Cubes',\n      },\n    ],\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'Digital Marketing Blog | Expert Insights & Tips | Lunar Cubes',\n    description: 'Expert digital marketing insights, SEO tips, and growth strategies for Nepali businesses.',\n    images: ['https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'],\n  },\n  alternates: {\n    canonical: '/blog',\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n};\n\n// Server component to fetch blog data\nasync function getBlogPosts() {\n  try {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/data/blog-posts.json`, {\n      cache: 'force-cache', // Cache for better performance\n    });\n    if (!response.ok) throw new Error('Failed to fetch blog posts');\n    return response.json();\n  } catch (error) {\n    console.error('Error fetching blog posts:', error);\n    return [];\n  }\n}\n\nasync function getBlogCategories() {\n  try {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/data/blog-categories.json`, {\n      cache: 'force-cache',\n    });\n    if (!response.ok) throw new Error('Failed to fetch blog categories');\n    return response.json();\n  } catch (error) {\n    console.error('Error fetching blog categories:', error);\n    return [];\n  }\n}\n\ninterface BlogPageProps {\n  searchParams: {\n    category?: string;\n    tag?: string;\n    search?: string;\n    page?: string;\n  };\n}\n\nexport default async function BlogPage({ searchParams }: BlogPageProps) {\n  // Fetch data on the server for better SEO\n  const [blogPosts, categories] = await Promise.all([\n    getBlogPosts(),\n    getBlogCategories(),\n  ]);\n\n  // Filter posts based on search params\n  let filteredPosts = blogPosts;\n\n  if (searchParams.category) {\n    filteredPosts = filteredPosts.filter(\n      (post: any) => post.category.slug === searchParams.category\n    );\n  }\n\n  if (searchParams.tag) {\n    filteredPosts = filteredPosts.filter((post: any) =>\n      post.tags.some((tag: string) => \n        tag.toLowerCase().includes(searchParams.tag!.toLowerCase())\n      )\n    );\n  }\n\n  if (searchParams.search) {\n    const searchTerm = searchParams.search.toLowerCase();\n    filteredPosts = filteredPosts.filter((post: any) =>\n      post.title.toLowerCase().includes(searchTerm) ||\n      post.excerpt.toLowerCase().includes(searchTerm) ||\n      post.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm))\n    );\n  }\n\n  // Pagination\n  const page = parseInt(searchParams.page || '1');\n  const postsPerPage = 9;\n  const totalPosts = filteredPosts.length;\n  const totalPages = Math.ceil(totalPosts / postsPerPage);\n  const startIndex = (page - 1) * postsPerPage;\n  const paginatedPosts = filteredPosts.slice(startIndex, startIndex + postsPerPage);\n\n  // Structured data for SEO\n  const structuredData = {\n    '@context': 'https://schema.org',\n    '@type': 'Blog',\n    name: 'Lunar Cubes Digital Marketing Blog',\n    description: 'Expert digital marketing insights, SEO tips, and growth strategies for Nepali businesses.',\n    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog`,\n    publisher: {\n      '@type': 'Organization',\n      name: 'Lunar Cubes',\n      logo: {\n        '@type': 'ImageObject',\n        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/images/logo.svg`,\n      },\n    },\n    blogPost: paginatedPosts.map((post: any) => ({\n      '@type': 'BlogPosting',\n      headline: post.title,\n      description: post.excerpt,\n      url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog/${post.slug}`,\n      datePublished: post.publishedAt,\n      dateModified: post.updatedAt,\n      author: {\n        '@type': 'Person',\n        name: post.author.name,\n      },\n      image: post.featuredImage,\n    })),\n  };\n\n  return (\n    <>\n      {/* Structured Data */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n      />\n      \n      <div>\n        <BlogHero />\n        <BlogCategories \n          categories={categories} \n          activeCategory={searchParams.category} \n        />\n        <BlogList \n          posts={paginatedPosts}\n          totalPages={totalPages}\n          currentPage={page}\n          searchParams={searchParams}\n        />\n        <BlogNewsletter />\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IACP,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,MAAM;QACN,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA+G;IAC1H;IACA,YAAY;QACV,WAAW;IACb;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEA,sCAAsC;AACtC,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB,qBAAqB,CAAC,EAAE;YAClH,OAAO;QACT;QACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAClC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB,0BAA0B,CAAC,EAAE;YACvH,OAAO;QACT;QACA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAClC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,EAAE;IACX;AACF;AAWe,eAAe,SAAS,EAAE,YAAY,EAAiB;IACpE,0CAA0C;IAC1C,MAAM,CAAC,WAAW,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;QAChD;QACA;KACD;IAED,sCAAsC;IACtC,IAAI,gBAAgB;IAEpB,IAAI,aAAa,QAAQ,EAAE;QACzB,gBAAgB,cAAc,MAAM,CAClC,CAAC,OAAc,KAAK,QAAQ,CAAC,IAAI,KAAK,aAAa,QAAQ;IAE/D;IAEA,IAAI,aAAa,GAAG,EAAE;QACpB,gBAAgB,cAAc,MAAM,CAAC,CAAC,OACpC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MACd,IAAI,WAAW,GAAG,QAAQ,CAAC,aAAa,GAAG,CAAE,WAAW;IAG9D;IAEA,IAAI,aAAa,MAAM,EAAE;QACvB,MAAM,aAAa,aAAa,MAAM,CAAC,WAAW;QAClD,gBAAgB,cAAc,MAAM,CAAC,CAAC,OACpC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MAAgB,IAAI,WAAW,GAAG,QAAQ,CAAC;IAE/D;IAEA,aAAa;IACb,MAAM,OAAO,SAAS,aAAa,IAAI,IAAI;IAC3C,MAAM,eAAe;IACrB,MAAM,aAAa,cAAc,MAAM;IACvC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAC1C,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;IAChC,MAAM,iBAAiB,cAAc,KAAK,CAAC,YAAY,aAAa;IAEpE,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aAAa;QACb,KAAK,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB,KAAK,CAAC;QAC1E,WAAW;YACT,SAAS;YACT,MAAM;YACN,MAAM;gBACJ,SAAS;gBACT,KAAK,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB,gBAAgB,CAAC;YACvF;QACF;QACA,UAAU,eAAe,GAAG,CAAC,CAAC,OAAc,CAAC;gBAC3C,SAAS;gBACT,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,OAAO;gBACzB,KAAK,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,wBAAwB,MAAM,EAAE,KAAK,IAAI,EAAE;gBACvF,eAAe,KAAK,WAAW;gBAC/B,cAAc,KAAK,SAAS;gBAC5B,QAAQ;oBACN,SAAS;oBACT,MAAM,KAAK,MAAM,CAAC,IAAI;gBACxB;gBACA,OAAO,KAAK,aAAa;YAC3B,CAAC;IACH;IAEA,qBACE;;0BAEE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBAAE,QAAQ,KAAK,SAAS,CAAC;gBAAgB;;;;;;0BAGpE,8OAAC;;kCACC,8OAAC,8IAAA,CAAA,UAAQ;;;;;kCACT,8OAAC,oJAAA,CAAA,UAAc;wBACb,YAAY;wBACZ,gBAAgB,aAAa,QAAQ;;;;;;kCAEvC,8OAAC,8IAAA,CAAA,UAAQ;wBACP,OAAO;wBACP,YAAY;wBACZ,aAAa;wBACb,cAAc;;;;;;kCAEhB,8OAAC,oJAAA,CAAA,UAAc;;;;;;;;;;;;;AAIvB", "debugId": null}}]}