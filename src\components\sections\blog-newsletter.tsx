'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { Mail, Send, CheckCircle, TrendingUp, Users, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function BlogNewsletter() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail('');
    }, 1500);
  };

  const benefits = [
    {
      icon: TrendingUp,
      title: 'Latest Trends',
      description: 'Get the newest digital marketing trends delivered to your inbox'
    },
    {
      icon: Users,
      title: 'Expert Insights',
      description: 'Learn from our team of digital marketing experts and industry leaders'
    },
    {
      icon: Award,
      title: 'Exclusive Content',
      description: 'Access subscriber-only content, case studies, and actionable tips'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-brand-navy to-brand-navy-dark relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <div className="inline-flex items-center px-4 py-2 bg-brand-gold/20 text-brand-gold rounded-full text-sm font-medium mb-6">
              <Mail className="w-4 h-4 mr-2" />
              Newsletter Subscription
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
              Stay Ahead of the
              <span className="block text-brand-gold">Digital Marketing Game</span>
            </h2>
            
            <p className="text-blue-100 text-lg md:text-xl leading-relaxed max-w-3xl mx-auto">
              Join 5,000+ business owners and marketers who receive our weekly newsletter 
              packed with actionable insights, case studies, and the latest trends in digital marketing.
            </p>
          </motion.div>

          {/* Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-brand-gold/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <benefit.icon className="h-8 w-8 text-brand-gold" />
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{benefit.title}</h3>
                <p className="text-blue-100 leading-relaxed">{benefit.description}</p>
              </motion.div>
            ))}
          </motion.div>

          {/* Newsletter Form */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            {!isSubscribed ? (
              <form onSubmit={handleSubmit} className="bg-white/10 backdrop-blur-sm rounded-2xl p-2 border border-white/20">
                <div className="flex flex-col md:flex-row gap-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address..."
                    className="flex-1 px-6 py-4 bg-white/90 text-gray-900 placeholder-gray-500 rounded-xl border-none outline-none text-lg"
                    required
                  />
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="px-8 py-4 bg-brand-gold hover:bg-brand-gold/90 text-gray-900 font-semibold rounded-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-5 h-5 border-2 border-gray-900/30 border-t-gray-900 rounded-full animate-spin"></div>
                        <span>Subscribing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Send className="h-5 w-5" />
                        <span>Subscribe Now</span>
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-green-500/20 border border-green-500/30 rounded-2xl p-8 text-center"
              >
                <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">Successfully Subscribed!</h3>
                <p className="text-green-100">
                  Thank you for subscribing! You'll receive our next newsletter with the latest 
                  digital marketing insights and tips.
                </p>
              </motion.div>
            )}

            {!isSubscribed && (
              <p className="text-blue-100/80 text-sm mt-4">
                No spam, ever. Unsubscribe anytime with just one click. 
                We respect your privacy and will never share your email.
              </p>
            )}
          </motion.div>

          {/* Social Proof */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
            className="mt-12 flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8 text-blue-100"
          >
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-brand-gold" />
              <span className="font-semibold">5,000+ Subscribers</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-brand-gold" />
              <span className="font-semibold">Weekly Insights</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-brand-gold" />
              <span className="font-semibold">Expert Content</span>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
