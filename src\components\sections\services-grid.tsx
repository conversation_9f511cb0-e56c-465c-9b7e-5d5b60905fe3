'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Share2, Search, Code, Target, PenTool, Palette, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useServices, useCaseStudiesByService } from '@/lib/queries/hooks';
import { Loading } from '@/components/ui/loading';

const iconMap = {
  Share2,
  Search,
  Code,
  Target,
  PenTool,
  Palette,
};

export default function ServicesGrid() {
  const { data: services, isLoading, error } = useServices();

  if (isLoading) {
    return (
      <section className="py-20 bg-white" id="services">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Loading size="lg" text="Loading services..." />
          </div>
        </div>
      </section>
    );
  }

  if (error || !services) {
    return (
      <section className="py-20 bg-white" id="services">
        <div className="container mx-auto px-4 text-center">
          <p className="text-red-600">Failed to load services. Please try again later.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-white" id="services">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Digital Marketing
            <span className="block text-brand-navy">Services</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Each service is carefully crafted to address the unique challenges and opportunities 
            in the Nepali market. We combine global best practices with local insights.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="space-y-16">
          {services.map((service, index) => {
            const IconComponent = iconMap[service.icon as keyof typeof iconMap] || Share2;
            const isEven = index % 2 === 0;
            
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                id={service.id}
                className="scroll-mt-20"
              >
                <div className={`grid lg:grid-cols-2 gap-12 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}>
                  {/* Content */}
                  <div className={isEven ? 'lg:order-1' : 'lg:order-2'}>
                    <div className="inline-flex items-center px-4 py-2 bg-brand-gold/10 text-brand-navy rounded-full text-sm font-medium mb-6">
                      <IconComponent className="w-4 h-4 mr-2 text-brand-gold" />
                      {service.title}
                    </div>
                    
                    <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                      {service.title}
                    </h3>
                    
                    <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Features */}
                    <div className="space-y-3 mb-8">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-start space-x-3">
                          <CheckCircle className="h-5 w-5 text-brand-gold mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Pricing */}
                    {service.pricing && (
                      <div className="bg-gray-50 rounded-2xl p-6 mb-8">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Pricing Plans</h4>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">Basic</div>
                            <div className="text-xl font-bold text-brand-navy">
                              NPR {service.pricing.basic.toLocaleString()}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">Standard</div>
                            <div className="text-xl font-bold text-brand-navy">
                              NPR {service.pricing.standard.toLocaleString()}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-sm text-gray-600 mb-1">Premium</div>
                            <div className="text-xl font-bold text-brand-navy">
                              NPR {service.pricing.premium.toLocaleString()}
                            </div>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 text-center mt-2">
                          Monthly pricing • Custom packages available
                        </div>
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button 
                        asChild 
                        className="bg-brand-navy hover:bg-brand-navy-dark text-white"
                      >
                        <Link href="/contact">
                          Get Started
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                      <Button 
                        asChild 
                        variant="outline" 
                        className="border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
                      >
                        <Link href="/contact">
                          Learn More
                        </Link>
                      </Button>
                    </div>
                  </div>

                  {/* Visual/Card */}
                  <div className={isEven ? 'lg:order-2' : 'lg:order-1'}>
                    <Card className="border-0 shadow-2xl overflow-hidden">
                      <div className="bg-gradient-to-br from-brand-navy to-brand-navy-dark p-8 text-white">
                        <div className="flex items-center justify-between mb-6">
                          <IconComponent className="h-12 w-12 text-brand-gold" />
                          <Badge className="bg-brand-gold text-gray-900">
                            Popular Service
                          </Badge>
                        </div>
                        
                        <h4 className="text-2xl font-bold mb-4">{service.title}</h4>
                        <p className="text-blue-100 mb-6">{service.shortDescription}</p>
                        
                        {/* Mock Results */}
                        <div className="space-y-4">
                          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm text-blue-100">Performance Increase</span>
                              <span className="text-xl font-bold text-brand-gold">+250%</span>
                            </div>
                            <div className="w-full bg-white/20 rounded-full h-2">
                              <div className="bg-brand-gold h-2 rounded-full w-4/5"></div>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                              <div className="text-2xl font-bold text-brand-gold">150+</div>
                              <div className="text-xs text-blue-100">Happy Clients</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                              <div className="text-2xl font-bold text-brand-gold">98%</div>
                              <div className="text-xs text-blue-100">Success Rate</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <CardContent className="p-6">
                        <h5 className="font-semibold text-gray-900 mb-3">What&apos;s Included:</h5>
                        <div className="space-y-2">
                          {service.features.slice(0, 4).map((feature, idx) => (
                            <div key={idx} className="flex items-center text-sm text-gray-600">
                              <div className="w-2 h-2 bg-brand-gold rounded-full mr-3 flex-shrink-0"></div>
                              {feature}
                            </div>
                          ))}
                          {service.features.length > 4 && (
                            <div className="text-sm text-brand-navy font-medium">
                              +{service.features.length - 4} more features included
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
