{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/query-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { useState } from 'react';\n\nexport default function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            // With SSR, we usually want to set some default staleTime\n            // above 0 to avoid refetching immediately on the client\n            staleTime: 60 * 1000, // 1 minute\n            retry: (failureCount, error) => {\n              // Don't retry on 4xx errors\n              if (error instanceof Error && error.message.includes('4')) {\n                return false;\n              }\n              // Retry up to 3 times for other errors\n              return failureCount < 3;\n            },\n            refetchOnWindowFocus: false,\n          },\n          mutations: {\n            retry: 1,\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMe,SAAS,cAAc,EAAE,QAAQ,EAAiC;IAC/E,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,6KAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,0DAA0D;oBAC1D,wDAAwD;oBACxD,WAAW,KAAK;oBAChB,OAAO,CAAC,cAAc;wBACpB,4BAA4B;wBAC5B,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;4BACzD,OAAO;wBACT;wBACA,uCAAuC;wBACvC,OAAO,eAAe;oBACxB;oBACA,sBAAsB;gBACxB;gBACA,WAAW;oBACT,OAAO;gBACT;YACF;QACF;IAGJ,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,8OAAC,oLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X, Phone, Mail } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport default function Header() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const pathname = usePathname();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  useEffect(() => {\n    setIsOpen(false);\n  }, [pathname]);\n\n  return (\n    <>\n      {/* Top Bar */}\n      <div className=\"bg-brand-navy text-white py-2 px-4 text-sm hidden md:block\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-2\">\n              <Phone className=\"h-4 w-4\" />\n              <span>+977-1-4441234</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Mail className=\"h-4 w-4\" />\n              <span><EMAIL></span>\n            </div>\n          </div>\n          <div className=\"text-brand-gold\">\n            From Concept to Cosmos\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <header\n        className={cn(\n          'sticky top-0 z-50 w-full border-b transition-all duration-300',\n          isScrolled\n            ? 'bg-white/95 backdrop-blur-md shadow-md'\n            : 'bg-white'\n        )}\n      >\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex h-16 items-center justify-between\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"h-10 w-10 relative\">\n                <Image\n                  src=\"/images/logo.svg\"\n                  alt=\"Lunar Cubes Logo\"\n                  fill\n                  className=\"object-contain\"\n                />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-brand-navy\">Lunar Cubes</h1>\n                <p className=\"text-xs text-gray-600\">Digital Marketing</p>\n              </div>\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    'text-sm font-medium transition-colors hover:text-brand-navy relative',\n                    pathname === item.href\n                      ? 'text-brand-navy'\n                      : 'text-gray-700'\n                  )}\n                >\n                  {item.name}\n                  {pathname === item.href && (\n                    <motion.div\n                      className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-brand-gold\"\n                      layoutId=\"activeTab\"\n                      initial={false}\n                      transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                    />\n                  )}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              <Button asChild className=\"bg-brand-navy hover:bg-brand-navy-dark\">\n                <Link href=\"/contact\">Get Started</Link>\n              </Button>\n            </div>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"md:hidden p-2 rounded-md text-gray-700 hover:text-brand-navy hover:bg-gray-100 transition-colors\"\n              aria-label=\"Toggle menu\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"md:hidden border-t bg-white\"\n            >\n              <div className=\"container mx-auto px-4 py-4 space-y-4\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={cn(\n                      'block py-2 text-base font-medium transition-colors',\n                      pathname === item.href\n                        ? 'text-brand-navy border-l-4 border-brand-gold pl-4'\n                        : 'text-gray-700 hover:text-brand-navy pl-4'\n                    )}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n                <div className=\"pt-4 border-t\">\n                  <Button asChild className=\"w-full bg-brand-navy hover:bg-brand-navy-dark\">\n                    <Link href=\"/contact\">Get Started</Link>\n                  </Button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </header>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;IACZ,GAAG;QAAC;KAAS;IAEb,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA,aACI,2CACA;;kCAGN,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAKzC,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,aAAa,KAAK,IAAI,GAClB,oBACA;;gDAGL,KAAK,IAAI;gDACT,aAAa,KAAK,IAAI,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,UAAS;oDACT,SAAS;oDACT,YAAY;wDAAE,MAAM;wDAAU,WAAW;wDAAK,SAAS;oDAAG;;;;;;;2CAfzD,KAAK,IAAI;;;;;;;;;;8CAuBpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,WAAU;kDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;;;;;;8CAK1B,8OAAC;oCACC,SAAS,IAAM,UAAU,CAAC;oCAC1B,WAAU;oCACV,cAAW;8CAEV,uBAAS,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAM5D,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,aAAa,KAAK,IAAI,GAClB,sDACA;sDAGL,KAAK,IAAI;2CATL,KAAK,IAAI;;;;;kDAYlB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,WAAU;sDACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/queries/api.ts"], "sourcesContent": ["// API functions for fetching data from mock JSON files\nimport { \n  Service, \n  TeamMember, \n  Testimonial, \n  Portfolio, \n  Package, \n  Industry, \n  CaseStudy, \n  CompanyInfo \n} from '@/types';\n\n// Base API URL - in production this would be your actual API endpoint\nconst API_BASE = '/api';\n\n// Simulate API delay for realistic loading states\nconst simulateDelay = (ms: number = 500) => \n  new Promise(resolve => setTimeout(resolve, ms));\n\n// Generic fetch function with error handling\nasync function fetchData<T>(endpoint: string): Promise<T> {\n  await simulateDelay();\n  \n  try {\n    const response = await fetch(endpoint);\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(`Error fetching ${endpoint}:`, error);\n    throw new Error(`Failed to fetch data from ${endpoint}`);\n  }\n}\n\n// Company Information\nexport const fetchCompanyInfo = async (): Promise<CompanyInfo> => {\n  return fetchData<CompanyInfo>('/data/company.json');\n};\n\n// Services\nexport const fetchServices = async (): Promise<Service[]> => {\n  return fetchData<Service[]>('/data/services.json');\n};\n\nexport const fetchServiceById = async (id: string): Promise<Service | null> => {\n  const services = await fetchServices();\n  return services.find(service => service.id === id) || null;\n};\n\n// Team Members\nexport const fetchTeamMembers = async (): Promise<TeamMember[]> => {\n  return fetchData<TeamMember[]>('/data/team.json');\n};\n\nexport const fetchTeamMemberById = async (id: string): Promise<TeamMember | null> => {\n  const team = await fetchTeamMembers();\n  return team.find(member => member.id === id) || null;\n};\n\n// Testimonials\nexport const fetchTestimonials = async (): Promise<Testimonial[]> => {\n  return fetchData<Testimonial[]>('/data/testimonials.json');\n};\n\nexport const fetchTestimonialsByService = async (serviceId: string): Promise<Testimonial[]> => {\n  const testimonials = await fetchTestimonials();\n  return testimonials.filter(testimonial => \n    testimonial.serviceUsed.toLowerCase().includes(serviceId.toLowerCase())\n  );\n};\n\n// Portfolio\nexport const fetchPortfolio = async (): Promise<Portfolio[]> => {\n  return fetchData<Portfolio[]>('/data/portfolio.json');\n};\n\nexport const fetchPortfolioByCategory = async (category: string): Promise<Portfolio[]> => {\n  const portfolio = await fetchPortfolio();\n  return portfolio.filter(item => \n    item.category.toLowerCase().includes(category.toLowerCase())\n  );\n};\n\nexport const fetchPortfolioById = async (id: string): Promise<Portfolio | null> => {\n  const portfolio = await fetchPortfolio();\n  return portfolio.find(item => item.id === id) || null;\n};\n\n// Packages\nexport const fetchPackages = async (): Promise<Package[]> => {\n  return fetchData<Package[]>('/data/packages.json');\n};\n\nexport const fetchPackageById = async (id: string): Promise<Package | null> => {\n  const packages = await fetchPackages();\n  return packages.find(pkg => pkg.id === id) || null;\n};\n\n// Industries\nexport const fetchIndustries = async (): Promise<Industry[]> => {\n  return fetchData<Industry[]>('/data/industries.json');\n};\n\nexport const fetchIndustryById = async (id: string): Promise<Industry | null> => {\n  const industries = await fetchIndustries();\n  return industries.find(industry => industry.id === id) || null;\n};\n\n// Case Studies\nexport const fetchCaseStudies = async (): Promise<CaseStudy[]> => {\n  return fetchData<CaseStudy[]>('/data/case-studies.json');\n};\n\nexport const fetchCaseStudyById = async (id: string): Promise<CaseStudy | null> => {\n  const caseStudies = await fetchCaseStudies();\n  return caseStudies.find(study => study.id === id) || null;\n};\n\nexport const fetchCaseStudiesByService = async (serviceId: string): Promise<CaseStudy[]> => {\n  const caseStudies = await fetchCaseStudies();\n  return caseStudies.filter(study => study.serviceUsed === serviceId);\n};\n\n// Search functionality\nexport const searchContent = async (query: string): Promise<{\n  services: Service[];\n  portfolio: Portfolio[];\n  caseStudies: CaseStudy[];\n}> => {\n  const [services, portfolio, caseStudies] = await Promise.all([\n    fetchServices(),\n    fetchPortfolio(),\n    fetchCaseStudies()\n  ]);\n\n  const searchTerm = query.toLowerCase();\n\n  return {\n    services: services.filter(service => \n      service.title.toLowerCase().includes(searchTerm) ||\n      service.description.toLowerCase().includes(searchTerm) ||\n      service.features.some(feature => feature.toLowerCase().includes(searchTerm))\n    ),\n    portfolio: portfolio.filter(item =>\n      item.title.toLowerCase().includes(searchTerm) ||\n      item.description.toLowerCase().includes(searchTerm) ||\n      item.category.toLowerCase().includes(searchTerm)\n    ),\n    caseStudies: caseStudies.filter(study =>\n      study.title.toLowerCase().includes(searchTerm) ||\n      study.challenge.toLowerCase().includes(searchTerm) ||\n      study.solution.toLowerCase().includes(searchTerm)\n    )\n  };\n};\n\n// Contact form submission (mock)\nexport const submitContactForm = async (formData: any): Promise<{ success: boolean; message: string }> => {\n  await simulateDelay(1000);\n  \n  // Simulate form validation\n  if (!formData.name || !formData.email || !formData.message) {\n    throw new Error('Please fill in all required fields');\n  }\n\n  // Simulate success response\n  return {\n    success: true,\n    message: 'Thank you for your message! We will get back to you within 24 hours.'\n  };\n};\n\n// Newsletter subscription (mock)\nexport const subscribeNewsletter = async (email: string): Promise<{ success: boolean; message: string }> => {\n  await simulateDelay(800);\n  \n  if (!email || !email.includes('@')) {\n    throw new Error('Please enter a valid email address');\n  }\n\n  return {\n    success: true,\n    message: 'Successfully subscribed to our newsletter!'\n  };\n};\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;;;;;;;;;;;;;;;;AAYvD,sEAAsE;AACtE,MAAM,WAAW;AAEjB,kDAAkD;AAClD,MAAM,gBAAgB,CAAC,KAAa,GAAG,GACrC,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAE7C,6CAA6C;AAC7C,eAAe,UAAa,QAAgB;IAC1C,MAAM;IAEN,IAAI;QACF,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,EAAE;QAC7C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,UAAU;IACzD;AACF;AAGO,MAAM,mBAAmB;IAC9B,OAAO,UAAuB;AAChC;AAGO,MAAM,gBAAgB;IAC3B,OAAO,UAAqB;AAC9B;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,OAAO;AACxD;AAGO,MAAM,mBAAmB;IAC9B,OAAO,UAAwB;AACjC;AAEO,MAAM,sBAAsB,OAAO;IACxC,MAAM,OAAO,MAAM;IACnB,OAAO,KAAK,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,OAAO;AAClD;AAGO,MAAM,oBAAoB;IAC/B,OAAO,UAAyB;AAClC;AAEO,MAAM,6BAA6B,OAAO;IAC/C,MAAM,eAAe,MAAM;IAC3B,OAAO,aAAa,MAAM,CAAC,CAAA,cACzB,YAAY,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,WAAW;AAExE;AAGO,MAAM,iBAAiB;IAC5B,OAAO,UAAuB;AAChC;AAEO,MAAM,2BAA2B,OAAO;IAC7C,MAAM,YAAY,MAAM;IACxB,OAAO,UAAU,MAAM,CAAC,CAAA,OACtB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;AAE7D;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,YAAY,MAAM;IACxB,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO;AACnD;AAGO,MAAM,gBAAgB;IAC3B,OAAO,UAAqB;AAC9B;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW,MAAM;IACvB,OAAO,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,OAAO;AAChD;AAGO,MAAM,kBAAkB;IAC7B,OAAO,UAAsB;AAC/B;AAEO,MAAM,oBAAoB,OAAO;IACtC,MAAM,aAAa,MAAM;IACzB,OAAO,WAAW,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO;AAC5D;AAGO,MAAM,mBAAmB;IAC9B,OAAO,UAAuB;AAChC;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,cAAc,MAAM;IAC1B,OAAO,YAAY,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO;AACvD;AAEO,MAAM,4BAA4B,OAAO;IAC9C,MAAM,cAAc,MAAM;IAC1B,OAAO,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,KAAK;AAC3D;AAGO,MAAM,gBAAgB,OAAO;IAKlC,MAAM,CAAC,UAAU,WAAW,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC3D;QACA;QACA;KACD;IAED,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO;QACL,UAAU,SAAS,MAAM,CAAC,CAAA,UACxB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,WAAW,GAAG,QAAQ,CAAC;QAElE,WAAW,UAAU,MAAM,CAAC,CAAA,OAC1B,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QAEvC,aAAa,YAAY,MAAM,CAAC,CAAA,QAC9B,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACnC,MAAM,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,eACvC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE1C;AACF;AAGO,MAAM,oBAAoB,OAAO;IACtC,MAAM,cAAc;IAEpB,2BAA2B;IAC3B,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,OAAO,EAAE;QAC1D,MAAM,IAAI,MAAM;IAClB;IAEA,4BAA4B;IAC5B,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAGO,MAAM,sBAAsB,OAAO;IACxC,MAAM,cAAc;IAEpB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/queries/hooks.ts"], "sourcesContent": ["// TanStack Query hooks for data fetching\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport {\n  fetchCompanyInfo,\n  fetchServices,\n  fetchServiceById,\n  fetchTeamMembers,\n  fetchTeamMemberById,\n  fetchTestimonials,\n  fetchTestimonialsByService,\n  fetchPortfolio,\n  fetchPortfolioByCategory,\n  fetchPortfolioById,\n  fetchPackages,\n  fetchPackageById,\n  fetchIndustries,\n  fetchIndustryById,\n  fetchCaseStudies,\n  fetchCaseStudyById,\n  fetchCaseStudiesByService,\n  searchContent,\n  submitContactForm,\n  subscribeNewsletter\n} from './api';\n\n// Query keys for consistent caching\nexport const queryKeys = {\n  company: ['company'],\n  services: ['services'],\n  service: (id: string) => ['service', id],\n  team: ['team'],\n  teamMember: (id: string) => ['teamMember', id],\n  testimonials: ['testimonials'],\n  testimonialsByService: (serviceId: string) => ['testimonials', 'service', serviceId],\n  portfolio: ['portfolio'],\n  portfolioByCategory: (category: string) => ['portfolio', 'category', category],\n  portfolioItem: (id: string) => ['portfolio', id],\n  packages: ['packages'],\n  package: (id: string) => ['package', id],\n  industries: ['industries'],\n  industry: (id: string) => ['industry', id],\n  caseStudies: ['caseStudies'],\n  caseStudy: (id: string) => ['caseStudy', id],\n  caseStudiesByService: (serviceId: string) => ['caseStudies', 'service', serviceId],\n  search: (query: string) => ['search', query],\n};\n\n// Company Information\nexport const useCompanyInfo = () => {\n  return useQuery({\n    queryKey: queryKeys.company,\n    queryFn: fetchCompanyInfo,\n    staleTime: 1000 * 60 * 60, // 1 hour\n  });\n};\n\n// Services\nexport const useServices = () => {\n  return useQuery({\n    queryKey: queryKeys.services,\n    queryFn: fetchServices,\n    staleTime: 1000 * 60 * 30, // 30 minutes\n  });\n};\n\nexport const useService = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.service(id),\n    queryFn: () => fetchServiceById(id),\n    enabled: !!id,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\n// Team Members\nexport const useTeamMembers = () => {\n  return useQuery({\n    queryKey: queryKeys.team,\n    queryFn: fetchTeamMembers,\n    staleTime: 1000 * 60 * 60, // 1 hour\n  });\n};\n\nexport const useTeamMember = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.teamMember(id),\n    queryFn: () => fetchTeamMemberById(id),\n    enabled: !!id,\n    staleTime: 1000 * 60 * 60,\n  });\n};\n\n// Testimonials\nexport const useTestimonials = () => {\n  return useQuery({\n    queryKey: queryKeys.testimonials,\n    queryFn: fetchTestimonials,\n    staleTime: 1000 * 60 * 15, // 15 minutes\n  });\n};\n\nexport const useTestimonialsByService = (serviceId: string) => {\n  return useQuery({\n    queryKey: queryKeys.testimonialsByService(serviceId),\n    queryFn: () => fetchTestimonialsByService(serviceId),\n    enabled: !!serviceId,\n    staleTime: 1000 * 60 * 15,\n  });\n};\n\n// Portfolio\nexport const usePortfolio = () => {\n  return useQuery({\n    queryKey: queryKeys.portfolio,\n    queryFn: fetchPortfolio,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\nexport const usePortfolioByCategory = (category: string) => {\n  return useQuery({\n    queryKey: queryKeys.portfolioByCategory(category),\n    queryFn: () => fetchPortfolioByCategory(category),\n    enabled: !!category,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\nexport const usePortfolioItem = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.portfolioItem(id),\n    queryFn: () => fetchPortfolioById(id),\n    enabled: !!id,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\n// Packages\nexport const usePackages = () => {\n  return useQuery({\n    queryKey: queryKeys.packages,\n    queryFn: fetchPackages,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\nexport const usePackage = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.package(id),\n    queryFn: () => fetchPackageById(id),\n    enabled: !!id,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\n// Industries\nexport const useIndustries = () => {\n  return useQuery({\n    queryKey: queryKeys.industries,\n    queryFn: fetchIndustries,\n    staleTime: 1000 * 60 * 60,\n  });\n};\n\nexport const useIndustry = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.industry(id),\n    queryFn: () => fetchIndustryById(id),\n    enabled: !!id,\n    staleTime: 1000 * 60 * 60,\n  });\n};\n\n// Case Studies\nexport const useCaseStudies = () => {\n  return useQuery({\n    queryKey: queryKeys.caseStudies,\n    queryFn: fetchCaseStudies,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\nexport const useCaseStudy = (id: string) => {\n  return useQuery({\n    queryKey: queryKeys.caseStudy(id),\n    queryFn: () => fetchCaseStudyById(id),\n    enabled: !!id,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\nexport const useCaseStudiesByService = (serviceId: string) => {\n  return useQuery({\n    queryKey: queryKeys.caseStudiesByService(serviceId),\n    queryFn: () => fetchCaseStudiesByService(serviceId),\n    enabled: !!serviceId,\n    staleTime: 1000 * 60 * 30,\n  });\n};\n\n// Search\nexport const useSearch = (query: string) => {\n  return useQuery({\n    queryKey: queryKeys.search(query),\n    queryFn: () => searchContent(query),\n    enabled: !!query && query.length > 2,\n    staleTime: 1000 * 60 * 5, // 5 minutes\n  });\n};\n\n// Mutations\nexport const useContactForm = () => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: submitContactForm,\n    onSuccess: () => {\n      // Optionally invalidate related queries\n      queryClient.invalidateQueries({ queryKey: ['contacts'] });\n    },\n  });\n};\n\nexport const useNewsletterSubscription = () => {\n  return useMutation({\n    mutationFn: subscribeNewsletter,\n  });\n};\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;;;;;;;;;;;;;;;;AACzC;AAAA;AAAA;AACA;;;AAwBO,MAAM,YAAY;IACvB,SAAS;QAAC;KAAU;IACpB,UAAU;QAAC;KAAW;IACtB,SAAS,CAAC,KAAe;YAAC;YAAW;SAAG;IACxC,MAAM;QAAC;KAAO;IACd,YAAY,CAAC,KAAe;YAAC;YAAc;SAAG;IAC9C,cAAc;QAAC;KAAe;IAC9B,uBAAuB,CAAC,YAAsB;YAAC;YAAgB;YAAW;SAAU;IACpF,WAAW;QAAC;KAAY;IACxB,qBAAqB,CAAC,WAAqB;YAAC;YAAa;YAAY;SAAS;IAC9E,eAAe,CAAC,KAAe;YAAC;YAAa;SAAG;IAChD,UAAU;QAAC;KAAW;IACtB,SAAS,CAAC,KAAe;YAAC;YAAW;SAAG;IACxC,YAAY;QAAC;KAAa;IAC1B,UAAU,CAAC,KAAe;YAAC;YAAY;SAAG;IAC1C,aAAa;QAAC;KAAc;IAC5B,WAAW,CAAC,KAAe;YAAC;YAAa;SAAG;IAC5C,sBAAsB,CAAC,YAAsB;YAAC;YAAe;YAAW;SAAU;IAClF,QAAQ,CAAC,QAAkB;YAAC;YAAU;SAAM;AAC9C;AAGO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO;QAC3B,SAAS,4HAAA,CAAA,mBAAgB;QACzB,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ;QAC5B,SAAS,4HAAA,CAAA,gBAAa;QACtB,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC;QAC5B,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,IAAI;QACxB,SAAS,4HAAA,CAAA,mBAAgB;QACzB,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,UAAU,CAAC;QAC/B,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;QACnC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,kBAAkB;IAC7B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,YAAY;QAChC,SAAS,4HAAA,CAAA,oBAAiB;QAC1B,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,qBAAqB,CAAC;QAC1C,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD,EAAE;QAC1C,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,SAAS;QAC7B,SAAS,4HAAA,CAAA,iBAAc;QACvB,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,mBAAmB,CAAC;QACxC,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,2BAAwB,AAAD,EAAE;QACxC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,aAAa,CAAC;QAClC,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;QAClC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ;QAC5B,SAAS,4HAAA,CAAA,gBAAa;QACtB,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,OAAO,CAAC;QAC5B,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;QAChC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,gBAAgB;IAC3B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,UAAU;QAC9B,SAAS,4HAAA,CAAA,kBAAe;QACxB,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,QAAQ,CAAC;QAC7B,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;QACjC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,WAAW;QAC/B,SAAS,4HAAA,CAAA,mBAAgB;QACzB,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,SAAS,CAAC;QAC9B,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;QAClC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAEO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,oBAAoB,CAAC;QACzC,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,4BAAyB,AAAD,EAAE;QACzC,SAAS,CAAC,CAAC;QACX,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,UAAU,MAAM,CAAC;QAC3B,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE;QAC7B,SAAS,CAAC,CAAC,SAAS,MAAM,MAAM,GAAG;QACnC,WAAW,OAAO,KAAK;IACzB;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,4HAAA,CAAA,oBAAiB;QAC7B,WAAW;YACT,wCAAwC;YACxC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;QACzD;IACF;AACF;AAEO,MAAM,4BAA4B;IACvC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,4HAAA,CAAA,sBAAmB;IACjC;AACF", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/layout/footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { \n  MapPin, \n  Phone, \n  Mail, \n  Facebook, \n  Instagram, \n  Linkedin, \n  Twitter,\n  ArrowRight\n} from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { useCompanyInfo } from '@/lib/queries/hooks';\n\nconst services = [\n  { name: 'Social Media Marketing', href: '/services#social-media-marketing' },\n  { name: 'SEO Optimization', href: '/services#search-engine-optimization' },\n  { name: 'Web Development', href: '/services#web-development' },\n  { name: 'Google Ads', href: '/services#google-ads' },\n  { name: 'Content Marketing', href: '/services#content-marketing' },\n  { name: 'Brand Identity', href: '/services#brand-identity' },\n];\n\nconst quickLinks = [\n  { name: 'About Us', href: '/about' },\n  { name: 'Our Team', href: '/about#team' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Case Studies', href: '/case-studies' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n];\n\nconst industries = [\n  { name: 'Hospitality & Tourism', href: '/industries#hospitality-tourism' },\n  { name: 'Retail & E-commerce', href: '/industries#retail-ecommerce' },\n  { name: 'Healthcare & Wellness', href: '/industries#healthcare-wellness' },\n  { name: 'Technology & Startups', href: '/industries#technology-startups' },\n  { name: 'Food & Beverage', href: '/industries#food-beverage' },\n  { name: 'Professional Services', href: '/industries#professional-services' },\n];\n\nexport default function Footer() {\n  const { data: company } = useCompanyInfo();\n\n  const handleNewsletterSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Newsletter subscription logic will be implemented\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Section */}\n      <div className=\"bg-brand-navy py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n                Stay Updated with Digital Marketing Insights\n              </h3>\n              <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto\">\n                Get the latest digital marketing tips, industry insights, and exclusive offers \n                delivered to your inbox. Join 500+ Nepali business owners who trust our expertise.\n              </p>\n              <form onSubmit={handleNewsletterSubmit} className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <Input\n                  type=\"email\"\n                  placeholder=\"Enter your email address\"\n                  className=\"flex-1 bg-white text-gray-900\"\n                  required\n                />\n                <Button type=\"submit\" className=\"bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold\">\n                  Subscribe\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </form>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"flex items-center space-x-2 mb-6\">\n                <div className=\"h-10 w-10 bg-gradient-to-br from-brand-navy to-brand-gold rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">LC</span>\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">Lunar Cubes</h3>\n                  <p className=\"text-sm text-gray-400\">From Concept to Cosmos</p>\n                </div>\n              </div>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                Nepal&apos;s premier digital marketing agency helping SMEs reach new heights \n                in the digital landscape with innovative strategies and local market expertise.\n              </p>\n              \n              {/* Contact Info */}\n              {company && (\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <MapPin className=\"h-5 w-5 text-brand-gold flex-shrink-0\" />\n                    <span className=\"text-gray-300\">{company.location.address}, {company.location.city}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Phone className=\"h-5 w-5 text-brand-gold flex-shrink-0\" />\n                    <span className=\"text-gray-300\">{company.contact.phone}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Mail className=\"h-5 w-5 text-brand-gold flex-shrink-0\" />\n                    <span className=\"text-gray-300\">{company.contact.email}</span>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Services */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Our Services</h4>\n              <ul className=\"space-y-3\">\n                {services.map((service) => (\n                  <li key={service.name}>\n                    <Link \n                      href={service.href}\n                      className=\"text-gray-300 hover:text-brand-gold transition-colors duration-200\"\n                    >\n                      {service.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Quick Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Quick Links</h4>\n              <ul className=\"space-y-3\">\n                {quickLinks.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-gray-300 hover:text-brand-gold transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Industries */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Industries We Serve</h4>\n              <ul className=\"space-y-3\">\n                {industries.map((industry) => (\n                  <li key={industry.name}>\n                    <Link \n                      href={industry.href}\n                      className=\"text-gray-300 hover:text-brand-gold transition-colors duration-200\"\n                    >\n                      {industry.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Footer */}\n      <div className=\"border-t border-gray-800 py-6\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © {new Date().getFullYear()} Lunar Cubes. All rights reserved. | \n              <Link href=\"/privacy\" className=\"hover:text-brand-gold ml-1\">Privacy Policy</Link> | \n              <Link href=\"/terms\" className=\"hover:text-brand-gold ml-1\">Terms of Service</Link>\n            </div>\n            \n            {/* Social Links */}\n            {company?.social && (\n              <div className=\"flex space-x-4\">\n                {company.social.facebook && (\n                  <a \n                    href={company.social.facebook}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-400 hover:text-brand-gold transition-colors duration-200\"\n                    aria-label=\"Facebook\"\n                  >\n                    <Facebook className=\"h-5 w-5\" />\n                  </a>\n                )}\n                {company.social.instagram && (\n                  <a \n                    href={company.social.instagram}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-400 hover:text-brand-gold transition-colors duration-200\"\n                    aria-label=\"Instagram\"\n                  >\n                    <Instagram className=\"h-5 w-5\" />\n                  </a>\n                )}\n                {company.social.linkedin && (\n                  <a \n                    href={company.social.linkedin}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-400 hover:text-brand-gold transition-colors duration-200\"\n                    aria-label=\"LinkedIn\"\n                  >\n                    <Linkedin className=\"h-5 w-5\" />\n                  </a>\n                )}\n                {company.social.twitter && (\n                  <a \n                    href={company.social.twitter}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-400 hover:text-brand-gold transition-colors duration-200\"\n                    aria-label=\"Twitter\"\n                  >\n                    <Twitter className=\"h-5 w-5\" />\n                  </a>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAhBA;;;;;;;;AAkBA,MAAM,WAAW;IACf;QAAE,MAAM;QAA0B,MAAM;IAAmC;IAC3E;QAAE,MAAM;QAAoB,MAAM;IAAuC;IACzE;QAAE,MAAM;QAAmB,MAAM;IAA4B;IAC7D;QAAE,MAAM;QAAc,MAAM;IAAuB;IACnD;QAAE,MAAM;QAAqB,MAAM;IAA8B;IACjE;QAAE,MAAM;QAAkB,MAAM;IAA2B;CAC5D;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAS;IACnC;QAAE,MAAM;QAAY,MAAM;IAAc;IACxC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAyB,MAAM;IAAkC;IACzE;QAAE,MAAM;QAAuB,MAAM;IAA+B;IACpE;QAAE,MAAM;QAAyB,MAAM;IAAkC;IACzE;QAAE,MAAM;QAAyB,MAAM;IAAkC;IACzE;QAAE,MAAM;QAAmB,MAAM;IAA4B;IAC7D;QAAE,MAAM;QAAyB,MAAM;IAAoC;CAC5E;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEvC,MAAM,yBAAyB,CAAC;QAC9B,EAAE,cAAc;IAChB,oDAAoD;IACtD;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,8OAAC;oCAAK,UAAU;oCAAwB,WAAU;;sDAChD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;sDAEV,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,WAAU;;gDAAqE;8DAEnG,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;oCAMjD,yBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;;4DAAiB,QAAQ,QAAQ,CAAC,OAAO;4DAAC;4DAAG,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;;0DAEpF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAiB,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAiB,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAO9D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,QAAQ,IAAI;oDAClB,WAAU;8DAET,QAAQ,IAAI;;;;;;+CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;0CAa3B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,SAAS,IAAI;oDACnB,WAAU;8DAET,SAAS,IAAI;;;;;;+CALT,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAwB;oCAClC,IAAI,OAAO,WAAW;oCAAG;kDAC5B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA6B;;;;;;oCAAqB;kDAClF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA6B;;;;;;;;;;;;4BAI5D,SAAS,wBACR,8OAAC;gCAAI,WAAU;;oCACZ,QAAQ,MAAM,CAAC,QAAQ,kBACtB,8OAAC;wCACC,MAAM,QAAQ,MAAM,CAAC,QAAQ;wCAC7B,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;oCAGvB,QAAQ,MAAM,CAAC,SAAS,kBACvB,8OAAC;wCACC,MAAM,QAAQ,MAAM,CAAC,SAAS;wCAC9B,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;oCAGxB,QAAQ,MAAM,CAAC,QAAQ,kBACtB,8OAAC;wCACC,MAAM,QAAQ,MAAM,CAAC,QAAQ;wCAC7B,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;oCAGvB,QAAQ,MAAM,CAAC,OAAO,kBACrB,8OAAC;wCACC,MAAM,QAAQ,MAAM,CAAC,OAAO;wCAC5B,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/scroll-utils.ts"], "sourcesContent": ["// Smooth scrolling utilities\n\nexport const smoothScrollTo = (elementId: string, offset: number = 80) => {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n\n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n};\n\nexport const scrollToTop = () => {\n  window.scrollTo({\n    top: 0,\n    behavior: 'smooth'\n  });\n};\n\n// Intersection Observer hook for scroll animations\nexport const useScrollAnimation = () => {\n  const observerCallback = (entries: IntersectionObserverEntry[]) => {\n    entries.forEach((entry) => {\n      if (entry.isIntersecting) {\n        entry.target.classList.add('animate-in');\n      }\n    });\n  };\n\n  const observer = new IntersectionObserver(observerCallback, {\n    threshold: 0.1,\n    rootMargin: '0px 0px -50px 0px'\n  });\n\n  return observer;\n};\n\n// Scroll progress indicator\nexport const getScrollProgress = (): number => {\n  const scrollTop = window.pageYOffset;\n  const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n  return (scrollTop / docHeight) * 100;\n};\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;AAEtB,MAAM,iBAAiB,CAAC,WAAmB,SAAiB,EAAE;IACnE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAEO,MAAM,cAAc;IACzB,OAAO,QAAQ,CAAC;QACd,KAAK;QACL,UAAU;IACZ;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,mBAAmB,CAAC;QACxB,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,MAAM,cAAc,EAAE;gBACxB,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;YAC7B;QACF;IACF;IAEA,MAAM,WAAW,IAAI,qBAAqB,kBAAkB;QAC1D,WAAW;QACX,YAAY;IACd;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB;IAC/B,MAAM,YAAY,OAAO,WAAW;IACpC,MAAM,YAAY,SAAS,eAAe,CAAC,YAAY,GAAG,OAAO,WAAW;IAC5E,OAAO,AAAC,YAAY,YAAa;AACnC", "debugId": null}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/scroll-to-top.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { scrollToTop } from '@/lib/scroll-utils';\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n          className=\"fixed bottom-8 right-8 z-50\"\n        >\n          <Button\n            onClick={scrollToTop}\n            size=\"icon\"\n            className=\"w-12 h-12 rounded-full bg-brand-navy hover:bg-brand-navy-dark text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110\"\n            aria-label=\"Scroll to top\"\n          >\n            <ArrowUp className=\"h-5 w-5\" />\n          </Button>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS,6HAAA,CAAA,cAAW;gBACpB,MAAK;gBACL,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;AAM/B", "debugId": null}}, {"offset": {"line": 1734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/whatsapp-fab.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { MessageCircle, X, Phone, Mail } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\n\nexport default function WhatsAppFAB() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  const whatsappNumber = \"+9779841234567\";\n  const whatsappMessage = \"Hello! I'm interested in your digital marketing services. Can you help me?\";\n  const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(whatsappMessage)}`;\n\n  const contactOptions = [\n    {\n      icon: MessageCircle,\n      label: \"WhatsApp Chat\",\n      action: () => window.open(whatsappUrl, '_blank'),\n      color: \"bg-green-500 hover:bg-green-600\",\n      delay: 0.1\n    },\n    {\n      icon: Phone,\n      label: \"Call Us\",\n      action: () => window.open('tel:+97714441234', '_self'),\n      color: \"bg-blue-500 hover:bg-blue-600\",\n      delay: 0.2\n    },\n    {\n      icon: Mail,\n      label: \"Email Us\",\n      action: () => window.open('mailto:<EMAIL>', '_self'),\n      color: \"bg-purple-500 hover:bg-purple-600\",\n      delay: 0.3\n    }\n  ];\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <div className=\"fixed bottom-6 right-6 z-50\">\n          {/* Contact Options */}\n          <AnimatePresence>\n            {isOpen && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8, y: 20 }}\n                animate={{ opacity: 1, scale: 1, y: 0 }}\n                exit={{ opacity: 0, scale: 0.8, y: 20 }}\n                transition={{ duration: 0.3 }}\n                className=\"mb-4 space-y-3\"\n              >\n                {contactOptions.map((option, index) => (\n                  <motion.div\n                    key={option.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    transition={{ duration: 0.2, delay: option.delay }}\n                  >\n                    <Card className=\"shadow-lg border-0 overflow-hidden\">\n                      <CardContent className=\"p-0\">\n                        <Button\n                          onClick={option.action}\n                          className={`${option.color} text-white w-full justify-start px-4 py-3 rounded-lg transition-all duration-300 hover:scale-105`}\n                          variant=\"ghost\"\n                        >\n                          <option.icon className=\"h-5 w-5 mr-3\" />\n                          <span className=\"font-medium\">{option.label}</span>\n                        </Button>\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main FAB Button */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.8 }}\n            transition={{ duration: 0.3 }}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Button\n              onClick={() => setIsOpen(!isOpen)}\n              className={`w-16 h-16 rounded-full shadow-lg transition-all duration-300 ${\n                isOpen \n                  ? 'bg-red-500 hover:bg-red-600' \n                  : 'bg-green-500 hover:bg-green-600'\n              } text-white border-4 border-white`}\n              aria-label={isOpen ? \"Close contact options\" : \"Open contact options\"}\n            >\n              <AnimatePresence mode=\"wait\">\n                {isOpen ? (\n                  <motion.div\n                    key=\"close\"\n                    initial={{ rotate: -90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: 90, opacity: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <X className=\"h-6 w-6\" />\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"whatsapp\"\n                    initial={{ rotate: 90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: -90, opacity: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <MessageCircle className=\"h-6 w-6\" />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </Button>\n          </motion.div>\n\n          {/* Pulse Animation */}\n          {!isOpen && (\n            <motion.div\n              className=\"absolute inset-0 rounded-full bg-green-500 opacity-30\"\n              animate={{\n                scale: [1, 1.5, 1],\n                opacity: [0.3, 0, 0.3],\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n              }}\n            />\n          )}\n\n          {/* Tooltip */}\n          {!isOpen && (\n            <motion.div\n              initial={{ opacity: 0, x: 10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 1, duration: 0.3 }}\n              className=\"absolute right-20 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap shadow-lg\"\n            >\n              Need help? Chat with us!\n              <div className=\"absolute right-0 top-1/2 transform translate-x-1 -translate-y-1/2 w-0 h-0 border-l-4 border-l-gray-900 border-t-4 border-t-transparent border-b-4 border-b-transparent\"></div>\n            </motion.div>\n          )}\n        </div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,WAAW,GAAG,KAAK;gBAC5B,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB;IACvB,MAAM,kBAAkB;IACxB,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,mBAAmB,kBAAkB;IAExH,MAAM,iBAAiB;QACrB;YACE,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,aAAa;YACvC,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,oBAAoB;YAC9C,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,yLAAA,CAAA,kBAAe;8BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;4BAAK,GAAG;wBAAG;wBACzC,SAAS;4BAAE,SAAS;4BAAG,OAAO;4BAAG,GAAG;wBAAE;wBACtC,MAAM;4BAAE,SAAS;4BAAG,OAAO;4BAAK,GAAG;wBAAG;wBACtC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,UAAU;oCAAK,OAAO,OAAO,KAAK;gCAAC;0CAEjD,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,OAAO,MAAM;4CACtB,WAAW,GAAG,OAAO,KAAK,CAAC,iGAAiG,CAAC;4CAC7H,SAAQ;;8DAER,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAe,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;+BAd5C,OAAO,KAAK;;;;;;;;;;;;;;;8BAyB3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAK;8BAExB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,UAAU,CAAC;wBAC1B,WAAW,CAAC,6DAA6D,EACvE,SACI,gCACA,kCACL,iCAAiC,CAAC;wBACnC,cAAY,SAAS,0BAA0B;kCAE/C,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,QAAQ,CAAC;oCAAI,SAAS;gCAAE;gCACnC,SAAS;oCAAE,QAAQ;oCAAG,SAAS;gCAAE;gCACjC,MAAM;oCAAE,QAAQ;oCAAI,SAAS;gCAAE;gCAC/B,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;+BANT;;;;qDASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,QAAQ;oCAAI,SAAS;gCAAE;gCAClC,SAAS;oCAAE,QAAQ;oCAAG,SAAS;gCAAE;gCACjC,MAAM;oCAAE,QAAQ,CAAC;oCAAI,SAAS;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;+BANrB;;;;;;;;;;;;;;;;;;;;gBAcb,CAAC,wBACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;;;;;;gBAKH,CAAC,wBACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAG,UAAU;oBAAI;oBACtC,WAAU;;wBACX;sCAEC,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}