import { Metadata } from 'next';
import BlogHero from '@/components/sections/blog-hero';
import BlogList from '@/components/sections/blog-list';
import BlogCategories from '@/components/sections/blog-categories';
import BlogNewsletter from '@/components/sections/blog-newsletter';

// Server component for better SEO
export const metadata: Metadata = {
  title: 'Digital Marketing Blog | Expert Insights & Tips | Lunar Cubes Nepal',
  description: 'Stay updated with the latest digital marketing trends, SEO tips, social media strategies, and expert insights for Nepali businesses. Read our comprehensive blog.',
  keywords: [
    'digital marketing blog Nepal',
    'SEO tips Nepal',
    'social media marketing blog',
    'content marketing Nepal',
    'online marketing insights',
    'digital marketing trends Nepal',
    'marketing blog Kathmandu',
    'business growth tips Nepal'
  ].join(', '),
  openGraph: {
    title: 'Digital Marketing Blog | Expert Insights & Tips | Lunar Cubes',
    description: 'Expert digital marketing insights, SEO tips, and growth strategies for Nepali businesses. Stay ahead with our comprehensive blog.',
    url: '/blog',
    type: 'website',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        width: 1200,
        height: 630,
        alt: 'Digital Marketing Blog - Lunar Cubes',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Digital Marketing Blog | Expert Insights & Tips | Lunar Cubes',
    description: 'Expert digital marketing insights, SEO tips, and growth strategies for Nepali businesses.',
    images: ['https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'],
  },
  alternates: {
    canonical: '/blog',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// Server component to fetch blog data
async function getBlogPosts() {
  try {
    // In development, read from file system directly
    if (process.env.NODE_ENV === 'development') {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/blog-posts.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(fileContents);
    }

    // In production, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/blog-posts.json`, {
      cache: 'force-cache',
    });
    if (!response.ok) throw new Error('Failed to fetch blog posts');
    return response.json();
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return [];
  }
}

async function getBlogCategories() {
  try {
    // In development, read from file system directly
    if (process.env.NODE_ENV === 'development') {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/blog-categories.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(fileContents);
    }

    // In production, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/blog-categories.json`, {
      cache: 'force-cache',
    });
    if (!response.ok) throw new Error('Failed to fetch blog categories');
    return response.json();
  } catch (error) {
    console.error('Error fetching blog categories:', error);
    return [];
  }
}

interface BlogPageProps {
  searchParams: {
    category?: string;
    tag?: string;
    search?: string;
    page?: string;
  };
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  // Fetch data on the server for better SEO
  const [blogPosts, categories] = await Promise.all([
    getBlogPosts(),
    getBlogCategories(),
  ]);

  // Filter posts based on search params
  let filteredPosts = blogPosts;

  if (searchParams.category) {
    filteredPosts = filteredPosts.filter(
      (post: any) => post.category.slug === searchParams.category
    );
  }

  if (searchParams.tag) {
    filteredPosts = filteredPosts.filter((post: any) =>
      post.tags.some((tag: string) => 
        tag.toLowerCase().includes(searchParams.tag!.toLowerCase())
      )
    );
  }

  if (searchParams.search) {
    const searchTerm = searchParams.search.toLowerCase();
    filteredPosts = filteredPosts.filter((post: any) =>
      post.title.toLowerCase().includes(searchTerm) ||
      post.excerpt.toLowerCase().includes(searchTerm) ||
      post.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm))
    );
  }

  // Pagination
  const page = parseInt(searchParams.page || '1');
  const postsPerPage = 9;
  const totalPosts = filteredPosts.length;
  const totalPages = Math.ceil(totalPosts / postsPerPage);
  const startIndex = (page - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(startIndex, startIndex + postsPerPage);

  // Structured data for SEO
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Lunar Cubes Digital Marketing Blog',
    description: 'Expert digital marketing insights, SEO tips, and growth strategies for Nepali businesses.',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog`,
    publisher: {
      '@type': 'Organization',
      name: 'Lunar Cubes',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/images/logo.svg`,
      },
    },
    blogPost: paginatedPosts.map((post: any) => ({
      '@type': 'BlogPosting',
      headline: post.title,
      description: post.excerpt,
      url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/blog/${post.slug}`,
      datePublished: post.publishedAt,
      dateModified: post.updatedAt,
      author: {
        '@type': 'Person',
        name: post.author.name,
      },
      image: post.featuredImage,
    })),
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div>
        <BlogHero />
        <BlogCategories 
          categories={categories} 
          activeCategory={searchParams.category} 
        />
        <BlogList 
          posts={paginatedPosts}
          totalPages={totalPages}
          currentPage={page}
          searchParams={searchParams}
        />
        <BlogNewsletter />
      </div>
    </>
  );
}
