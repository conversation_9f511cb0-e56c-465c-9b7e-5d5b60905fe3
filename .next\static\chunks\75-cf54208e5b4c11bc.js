"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[75],{845:(t,e,i)=>{i.d(e,{t:()=>r});let r=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>r});let r=(0,i(2115).createContext)({})},920:(t,e,i)=>{i.d(e,{m:()=>n});var r=i(5910),s=i(2020),n=new class extends r.Q{#t;#e;#i;constructor(){super(),this.#i=t=>{if(!s.S$&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#i=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}}},1239:(t,e,i)=>{i.d(e,{t:()=>n});var r=i(5910),s=i(2020),n=new class extends r.Q{#r=!0;#e;#i;constructor(){super(),this.#i=t=>{if(!s.S$&&window.addEventListener){let e=()=>t(!0),i=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",i)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#i=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#r!==t&&(this.#r=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#r}}},1508:(t,e,i)=>{i.d(e,{Q:()=>r});let r=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2020:(t,e,i)=>{i.d(e,{Cp:()=>m,EN:()=>p,Eh:()=>u,F$:()=>d,GU:()=>P,MK:()=>h,S$:()=>r,ZM:()=>S,ZZ:()=>T,Zw:()=>n,d2:()=>l,f8:()=>f,gn:()=>o,hT:()=>k,j3:()=>a,lQ:()=>s,nJ:()=>c,pl:()=>x,y9:()=>w,yy:()=>b});var r="undefined"==typeof window||"Deno"in globalThis;function s(){}function n(t,e){return"function"==typeof t?t(e):t}function o(t){return"number"==typeof t&&t>=0&&t!==1/0}function a(t,e){return Math.max(t+(e||0)-Date.now(),0)}function l(t,e){return"function"==typeof t?t(e):t}function u(t,e){return"function"==typeof t?t(e):t}function h(t,e){let{type:i="all",exact:r,fetchStatus:s,predicate:n,queryKey:o,stale:a}=t;if(o){if(r){if(e.queryHash!==d(o,e.options))return!1}else if(!m(e.queryKey,o))return!1}if("all"!==i){let t=e.isActive();if("active"===i&&!t||"inactive"===i&&t)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(!s||s===e.state.fetchStatus)&&(!n||!!n(e))}function c(t,e){let{exact:i,status:r,predicate:s,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(i){if(p(e.options.mutationKey)!==p(n))return!1}else if(!m(e.options.mutationKey,n))return!1}return(!r||e.state.status===r)&&(!s||!!s(e))}function d(t,e){return(e?.queryKeyHashFn||p)(t)}function p(t){return JSON.stringify(t,(t,e)=>g(e)?Object.keys(e).sort().reduce((t,i)=>(t[i]=e[i],t),{}):e)}function m(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(i=>m(t[i],e[i]))}function f(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let i in t)if(t[i]!==e[i])return!1;return!0}function y(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function g(t){if(!v(t))return!1;let e=t.constructor;if(void 0===e)return!0;let i=e.prototype;return!!v(i)&&!!i.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function v(t){return"[object Object]"===Object.prototype.toString.call(t)}function b(t){return new Promise(e=>{setTimeout(e,t)})}function x(t,e,i){return"function"==typeof i.structuralSharing?i.structuralSharing(t,e):!1!==i.structuralSharing?function t(e,i){if(e===i)return e;let r=y(e)&&y(i);if(r||g(e)&&g(i)){let s=r?e:Object.keys(e),n=s.length,o=r?i:Object.keys(i),a=o.length,l=r?[]:{},u=new Set(s),h=0;for(let s=0;s<a;s++){let n=r?s:o[s];(!r&&u.has(n)||r)&&void 0===e[n]&&void 0===i[n]?(l[n]=void 0,h++):(l[n]=t(e[n],i[n]),l[n]===e[n]&&void 0!==e[n]&&h++)}return n===a&&h===n?e:l}return i}(t,e):e}function w(t,e,i=0){let r=[...t,e];return i&&r.length>i?r.slice(1):r}function T(t,e,i=0){let r=[e,...t];return i&&r.length>i?r.slice(0,-1):r}var k=Symbol();function S(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==k?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}function P(t,e){return"function"==typeof t?t(...e):!!t}},2082:(t,e,i)=>{i.d(e,{xQ:()=>n});var r=i(2115),s=i(845);function n(t=!0){let e=(0,r.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2085:(t,e,i)=>{i.d(e,{F:()=>o});var r=i(2596);let s=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,n=r.$,o=(t,e)=>i=>{var r;if((null==e?void 0:e.variants)==null)return n(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==i?void 0:i[t],r=null==a?void 0:a[t];if(null===e)return null;let n=s(e)||s(r);return o[t][n]}),u=i&&Object.entries(i).reduce((t,e)=>{let[i,r]=e;return void 0===r||(t[i]=r),t},{});return n(t,l,null==e||null==(r=e.compoundVariants)?void 0:r.reduce((t,e)=>{let{class:i,className:r,...s}=e;return Object.entries(s).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...a,...u}[e]):({...a,...u})[e]===i})?[...t,i,r]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},2596:(t,e,i)=>{i.d(e,{$:()=>r});function r(){for(var t,e,i=0,r="",s=arguments.length;i<s;i++)(t=arguments[i])&&(e=function t(e){var i,r,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(i=0;i<n;i++)e[i]&&(r=t(e[i]))&&(s&&(s+=" "),s+=r)}else for(r in e)e[r]&&(s&&(s+=" "),s+=r);return s}(t))&&(r&&(r+=" "),r+=e);return r}},2885:(t,e,i)=>{i.d(e,{M:()=>s});var r=i(2115);function s(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2960:(t,e,i)=>{i.d(e,{I:()=>b});var r=i(920),s=i(7165),n=i(9853),o=i(5910),a=i(3504),l=i(2020),u=class extends o.Q{constructor(t,e){super(),this.options=e,this.#s=t,this.#n=null,this.#o=(0,a.T)(),this.options.experimental_prefetchInRender||this.#o.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#s;#a=void 0;#l=void 0;#u=void 0;#h;#c;#o;#n;#d;#p;#m;#f;#y;#g;#v=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#a.addObserver(this),h(this.#a,this.options)?this.#b():this.updateResult(),this.#x())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return c(this.#a,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return c(this.#a,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#w(),this.#T(),this.#a.removeObserver(this)}setOptions(t){let e=this.options,i=this.#a;if(this.options=this.#s.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#a))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#k(),this.#a.setOptions(this.options),e._defaulted&&!(0,l.f8)(this.options,e)&&this.#s.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#a,observer:this});let r=this.hasListeners();r&&d(this.#a,i,this.options,e)&&this.#b(),this.updateResult(),r&&(this.#a!==i||(0,l.Eh)(this.options.enabled,this.#a)!==(0,l.Eh)(e.enabled,this.#a)||(0,l.d2)(this.options.staleTime,this.#a)!==(0,l.d2)(e.staleTime,this.#a))&&this.#S();let s=this.#P();r&&(this.#a!==i||(0,l.Eh)(this.options.enabled,this.#a)!==(0,l.Eh)(e.enabled,this.#a)||s!==this.#g)&&this.#A(s)}getOptimisticResult(t){var e,i;let r=this.#s.getQueryCache().build(this.#s,t),s=this.createResult(r,t);return e=this,i=s,(0,l.f8)(e.getCurrentResult(),i)||(this.#u=s,this.#c=this.options,this.#h=this.#a.state),s}getCurrentResult(){return this.#u}trackResult(t,e){return new Proxy(t,{get:(t,i)=>(this.trackProp(i),e?.(i),Reflect.get(t,i))})}trackProp(t){this.#v.add(t)}getCurrentQuery(){return this.#a}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#s.defaultQueryOptions(t),i=this.#s.getQueryCache().build(this.#s,e);return i.fetch().then(()=>this.createResult(i,e))}fetch(t){return this.#b({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#u))}#b(t){this.#k();let e=this.#a.fetch(this.options,t);return t?.throwOnError||(e=e.catch(l.lQ)),e}#S(){this.#w();let t=(0,l.d2)(this.options.staleTime,this.#a);if(l.S$||this.#u.isStale||!(0,l.gn)(t))return;let e=(0,l.j3)(this.#u.dataUpdatedAt,t);this.#f=setTimeout(()=>{this.#u.isStale||this.updateResult()},e+1)}#P(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#a):this.options.refetchInterval)??!1}#A(t){this.#T(),this.#g=t,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#a)&&(0,l.gn)(this.#g)&&0!==this.#g&&(this.#y=setInterval(()=>{(this.options.refetchIntervalInBackground||r.m.isFocused())&&this.#b()},this.#g))}#x(){this.#S(),this.#A(this.#P())}#w(){this.#f&&(clearTimeout(this.#f),this.#f=void 0)}#T(){this.#y&&(clearInterval(this.#y),this.#y=void 0)}createResult(t,e){let i,r=this.#a,s=this.options,o=this.#u,u=this.#h,c=this.#c,m=t!==r?t.state:this.#l,{state:f}=t,y={...f},g=!1;if(e._optimisticResults){let i=this.hasListeners(),o=!i&&h(t,e),a=i&&d(t,r,e,s);(o||a)&&(y={...y,...(0,n.k)(f.data,t.options)}),"isRestoring"===e._optimisticResults&&(y.fetchStatus="idle")}let{error:v,errorUpdatedAt:b,status:x}=y;i=y.data;let w=!1;if(void 0!==e.placeholderData&&void 0===i&&"pending"===x){let t;o?.isPlaceholderData&&e.placeholderData===c?.placeholderData?(t=o.data,w=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#m?.state.data,this.#m):e.placeholderData,void 0!==t&&(x="success",i=(0,l.pl)(o?.data,t,e),g=!0)}if(e.select&&void 0!==i&&!w)if(o&&i===u?.data&&e.select===this.#d)i=this.#p;else try{this.#d=e.select,i=e.select(i),i=(0,l.pl)(o?.data,i,e),this.#p=i,this.#n=null}catch(t){this.#n=t}this.#n&&(v=this.#n,i=this.#p,b=Date.now(),x="error");let T="fetching"===y.fetchStatus,k="pending"===x,S="error"===x,P=k&&T,A=void 0!==i,E={status:x,fetchStatus:y.fetchStatus,isPending:k,isSuccess:"success"===x,isError:S,isInitialLoading:P,isLoading:P,data:i,dataUpdatedAt:y.dataUpdatedAt,error:v,errorUpdatedAt:b,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>m.dataUpdateCount||y.errorUpdateCount>m.errorUpdateCount,isFetching:T,isRefetching:T&&!k,isLoadingError:S&&!A,isPaused:"paused"===y.fetchStatus,isPlaceholderData:g,isRefetchError:S&&A,isStale:p(t,e),refetch:this.refetch,promise:this.#o,isEnabled:!1!==(0,l.Eh)(e.enabled,t)};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===E.status?t.reject(E.error):void 0!==E.data&&t.resolve(E.data)},i=()=>{e(this.#o=E.promise=(0,a.T)())},s=this.#o;switch(s.status){case"pending":t.queryHash===r.queryHash&&e(s);break;case"fulfilled":("error"===E.status||E.data!==s.value)&&i();break;case"rejected":("error"!==E.status||E.error!==s.reason)&&i()}}return E}updateResult(){let t=this.#u,e=this.createResult(this.#a,this.options);if(this.#h=this.#a.state,this.#c=this.options,void 0!==this.#h.data&&(this.#m=this.#a),(0,l.f8)(e,t))return;this.#u=e;let i=()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,i="function"==typeof e?e():e;if("all"===i||!i&&!this.#v.size)return!0;let r=new Set(i??this.#v);return this.options.throwOnError&&r.add("error"),Object.keys(this.#u).some(e=>this.#u[e]!==t[e]&&r.has(e))};this.#E({listeners:i()})}#k(){let t=this.#s.getQueryCache().build(this.#s,this.options);if(t===this.#a)return;let e=this.#a;this.#a=t,this.#l=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#x()}#E(t){s.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#u)}),this.#s.getQueryCache().notify({query:this.#a,type:"observerResultsUpdated"})})}};function h(t,e){return!1!==(0,l.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&c(t,e,e.refetchOnMount)}function c(t,e,i){if(!1!==(0,l.Eh)(e.enabled,t)&&"static"!==(0,l.d2)(e.staleTime,t)){let r="function"==typeof i?i(t):i;return"always"===r||!1!==r&&p(t,e)}return!1}function d(t,e,i,r){return(t!==e||!1===(0,l.Eh)(r.enabled,t))&&(!i.suspense||"error"!==t.state.status)&&p(t,i)}function p(t,e){return!1!==(0,l.Eh)(e.enabled,t)&&t.isStaleByTime((0,l.d2)(e.staleTime,t))}var m=i(2115),f=i(6715);i(5155);var y=m.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),g=m.createContext(!1);g.Provider;var v=(t,e,i)=>e.fetchOptimistic(t).catch(()=>{i.clearReset()});function b(t,e){return function(t,e,i){var r,n,o,a,u;let h=m.useContext(g),c=m.useContext(y),d=(0,f.jE)(i),p=d.defaultQueryOptions(t);if(null==(n=d.getDefaultOptions().queries)||null==(r=n._experimental_beforeQuery)||r.call(n,p),p._optimisticResults=h?"isRestoring":"optimistic",p.suspense){let t=t=>"static"===t?t:Math.max(t??1e3,1e3),e=p.staleTime;p.staleTime="function"==typeof e?(...i)=>t(e(...i)):t(e),"number"==typeof p.gcTime&&(p.gcTime=Math.max(p.gcTime,1e3))}(p.suspense||p.throwOnError||p.experimental_prefetchInRender)&&!c.isReset()&&(p.retryOnMount=!1),m.useEffect(()=>{c.clearReset()},[c]);let b=!d.getQueryCache().get(p.queryHash),[x]=m.useState(()=>new e(d,p)),w=x.getOptimisticResult(p),T=!h&&!1!==t.subscribed;if(m.useSyncExternalStore(m.useCallback(t=>{let e=T?x.subscribe(s.jG.batchCalls(t)):l.lQ;return x.updateResult(),e},[x,T]),()=>x.getCurrentResult(),()=>x.getCurrentResult()),m.useEffect(()=>{x.setOptions(p)},[p,x]),p?.suspense&&w.isPending)throw v(p,x,c);if((t=>{let{result:e,errorResetBoundary:i,throwOnError:r,query:s,suspense:n}=t;return e.isError&&!i.isReset()&&!e.isFetching&&s&&(n&&void 0===e.data||(0,l.GU)(r,[e.error,s]))})({result:w,errorResetBoundary:c,throwOnError:p.throwOnError,query:d.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw w.error;if(null==(a=d.getDefaultOptions().queries)||null==(o=a._experimental_afterQuery)||o.call(a,p,w),p.experimental_prefetchInRender&&!l.S$&&w.isLoading&&w.isFetching&&!h){let t=b?v(p,x,c):null==(u=d.getQueryCache().get(p.queryHash))?void 0:u.promise;null==t||t.catch(l.lQ).finally(()=>{x.updateResult()})}return p.notifyOnChangeProps?w:x.trackResult(w)}(t,u,e)}},3463:(t,e,i)=>{let r;i.d(e,{P:()=>nm});var s=i(2115);let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(n),a=t=>180*t/Math.PI,l=t=>h(a(Math.atan2(t[1],t[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},h=t=>((t%=360)<0&&(t+=360),t),c=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),d=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:c,scaleY:d,scale:t=>(c(t)+d(t))/2,rotateX:t=>h(a(Math.atan2(t[6],t[5]))),rotateY:t=>h(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,r;if(!t||"none"===t)return m(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=p,r=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=u,r=e}if(!r)return m(e);let n=i[e],o=r[1].split(",").map(y);return"function"==typeof n?n(o):o[n]}function y(t){return parseFloat(t.trim())}let g=t=>e=>"string"==typeof e&&e.startsWith(t),v=g("--"),b=g("var(--"),x=t=>!!b(t)&&w.test(t.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function T({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}let k=(t,e,i)=>t+(e-t)*i;function S(t){return void 0===t||1===t}function P({scale:t,scaleX:e,scaleY:i}){return!S(t)||!S(e)||!S(i)}function A(t){return P(t)||E(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function E(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function C(t,e,i,r,s){return void 0!==s&&(t=r+s*(t-r)),r+i*(t-r)+e}function R(t,e=0,i=1,r,s){t.min=C(t.min,e,i,r,s),t.max=C(t.max,e,i,r,s)}function M(t,{x:e,y:i}){R(t.x,e.translate,e.scale,e.originPoint),R(t.y,i.translate,i.scale,i.originPoint)}function D(t,e){t.min=t.min+e,t.max=t.max+e}function V(t,e,i,r,s=.5){let n=k(t.min,t.max,s);R(t,e,i,n,r)}function j(t,e){V(t.x,e.x,e.scaleX,e.scale,e.originX),V(t.y,e.y,e.scaleY,e.scale,e.originY)}function F(t,e){return T(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let O=new Set(["width","height","top","left","right","bottom",...n]),L=(t,e,i)=>i>e?e:i<t?t:i,I={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},B={...I,transform:t=>L(0,1,t)},U={...I,default:1},z=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),N=z("deg"),$=z("%"),Q=z("px"),W=z("vh"),q=z("vw"),G={...$,parse:t=>$.parse(t)/100,transform:t=>$.transform(100*t)},H=t=>e=>e.test(t),_=[I,Q,$,N,q,W,{test:t=>"auto"===t,parse:t=>t}],K=t=>_.find(H(t)),Y=()=>{},X=()=>{},Z=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),J=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===I||t===Q,te=new Set(["x","y","z"]),ti=n.filter(t=>!te.has(t)),tr={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tr.translateX=tr.x,tr.translateY=tr.y;let ts=t=>t,tn={},to=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ta={value:null,addProjectionMetrics:null};function tl(t,e){let i=!1,r=!0,s={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,o=to.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,s=!1,n=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,n=!1)=>{let a=n&&s?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,s){n=!0;return}s=!0,[i,r]=[r,i],i.forEach(u),e&&ta.value&&ta.value.frameloop[e].push(l),l=0,i.clear(),s=!1,n&&(n=!1,h.process(t))}};return h}(n,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:p,postRender:m}=o,f=()=>{let n=tn.useManualTiming?s.timestamp:performance.now();i=!1,tn.useManualTiming||(s.delta=r?1e3/60:Math.max(Math.min(n-s.timestamp,40),1)),s.timestamp=n,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),h.process(s),c.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,i&&e&&(r=!1,t(f))};return{schedule:to.reduce((e,n)=>{let a=o[n];return e[n]=(e,n=!1,o=!1)=>(!i&&(i=!0,r=!0,s.isProcessing||t(f)),a.schedule(e,n,o)),e},{}),cancel:t=>{for(let e=0;e<to.length;e++)o[to[e]].cancel(t)},state:s,steps:o}}let{schedule:tu,cancel:th,state:tc,steps:td}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ts,!0),tp=new Set,tm=!1,tf=!1,ty=!1;function tg(){if(tf){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ti.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tf=!1,tm=!1,tp.forEach(t=>t.complete(ty)),tp.clear()}function tv(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tf=!0)})}class tb{constructor(t,e,i,r,s,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=s,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tm||(tm=!0,tu.read(tv),tu.resolveKeyframes(tg))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let s=r?.get(),n=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let r=i.readValue(e,n);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=n),r&&void 0===s&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tx=t=>/^0[^.\s]+$/u.test(t),tw=t=>Math.round(1e5*t)/1e5,tT=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tk=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tS=(t,e)=>i=>!!("string"==typeof i&&tk.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tP=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,n,o,a]=r.match(tT);return{[t]:parseFloat(s),[e]:parseFloat(n),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tA={...I,transform:t=>Math.round(L(0,255,t))},tE={test:tS("rgb","red"),parse:tP("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tA.transform(t)+", "+tA.transform(e)+", "+tA.transform(i)+", "+tw(B.transform(r))+")"},tC={test:tS("#"),parse:function(t){let e="",i="",r="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,r+=r,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:tE.transform},tR={test:tS("hsl","hue"),parse:tP("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+$.transform(tw(e))+", "+$.transform(tw(i))+", "+tw(B.transform(r))+")"},tM={test:t=>tE.test(t)||tC.test(t)||tR.test(t),parse:t=>tE.test(t)?tE.parse(t):tR.test(t)?tR.parse(t):tC.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tE.transform(t):tR.transform(t),getAnimatableNone:t=>{let e=tM.parse(t);return e.alpha=0,tM.transform(e)}},tD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tV="number",tj="color",tF=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tO(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],n=0,o=e.replace(tF,t=>(tM.test(t)?(r.color.push(n),s.push(tj),i.push(tM.parse(t))):t.startsWith("var(")?(r.var.push(n),s.push("var"),i.push(t)):(r.number.push(n),s.push(tV),i.push(parseFloat(t))),++n,"${}")).split("${}");return{values:i,split:o,indexes:r,types:s}}function tL(t){return tO(t).values}function tI(t){let{split:e,types:i}=tO(t),r=e.length;return t=>{let s="";for(let n=0;n<r;n++)if(s+=e[n],void 0!==t[n]){let e=i[n];e===tV?s+=tw(t[n]):e===tj?s+=tM.transform(t[n]):s+=t[n]}return s}}let tB=t=>"number"==typeof t?0:tM.test(t)?tM.getAnimatableNone(t):t,tU={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tT)?.length||0)+(t.match(tD)?.length||0)>0},parse:tL,createTransformer:tI,getAnimatableNone:function(t){let e=tL(t);return tI(t)(e.map(tB))}},tz=new Set(["brightness","contrast","saturate","opacity"]);function tN(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(tT)||[];if(!r)return t;let s=i.replace(r,""),n=+!!tz.has(e);return r!==i&&(n*=100),e+"("+n+s+")"}let t$=/\b([a-z-]*)\(.*?\)/gu,tQ={...tU,getAnimatableNone:t=>{let e=t.match(t$);return e?e.map(tN).join(" "):t}},tW={...I,transform:Math.round},tq={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,backgroundPositionX:Q,backgroundPositionY:Q,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:N,skewX:N,skewY:N,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:B,originX:G,originY:G,originZ:Q,zIndex:tW,fillOpacity:B,strokeOpacity:B,numOctaves:tW},tG={...tq,color:tM,backgroundColor:tM,outlineColor:tM,fill:tM,stroke:tM,borderColor:tM,borderTopColor:tM,borderRightColor:tM,borderBottomColor:tM,borderLeftColor:tM,filter:tQ,WebkitFilter:tQ},tH=t=>tG[t];function t_(t,e){let i=tH(t);return i!==tQ&&(i=tU),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tK=new Set(["auto","none","0"]);class tY extends tb{constructor(t,e,i,r,s){super(t,e,i,r,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&x(r=r.trim())){let s=function t(e,i,r=1){X(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[s,n]=function(t){let e=J.exec(t);if(!e)return[,];let[,i,r,s]=e;return[`--${i??r}`,s]}(e);if(!s)return;let o=window.getComputedStyle(i).getPropertyValue(s);if(o){let t=o.trim();return Z(t)?parseFloat(t):t}return x(n)?t(n,i,r+1):n}(r,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!O.has(i)||2!==t.length)return;let[r,s]=t,n=K(r),o=K(s);if(n!==o)if(tt(n)&&tt(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tx(r)))&&i.push(e)}i.length&&function(t,e,i){let r,s=0;for(;s<t.length&&!r;){let e=t[s];"string"==typeof e&&!tK.has(e)&&tO(e).values.length&&(r=t[s]),s++}if(r&&i)for(let s of e)t[s]=t_(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let s=i.length-1,n=i[s];i[s]=tr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tX=t=>!!(t&&t.getVelocity);function tZ(){r=void 0}let tJ={now:()=>(void 0===r&&tJ.set(tc.isProcessing||tn.useManualTiming?tc.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(tZ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t2{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let s=0;s<r;s++){let r=this.subscriptions[s];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t5={current:void 0};class t3{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=tJ.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tJ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t2);let i=this.events[t].add(e);return"change"===t?()=>{i(),tu.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t5.current&&t5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tJ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t9(t,e){return new t3(t,e)}let t4=[..._,tM,tU],{schedule:t8}=tl(queueMicrotask,!1),t6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t7={};for(let t in t6)t7[t]={isEnabled:e=>t6[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),ei=()=>({min:0,max:0}),er=()=>({x:ei(),y:ei()});var es=i(8972);let en={current:null},eo={current:!1},ea=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eu(t){return"string"==typeof t||Array.isArray(t)}let eh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ec=["initial",...eh];function ed(t){return el(t.animate)||ec.some(e=>eu(t[e]))}function ep(t){return!!(ed(t)||t.variants)}function em(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function ef(t,e,i,r){if("function"==typeof e){let[s,n]=em(r);e=e(void 0!==i?i:t.custom,s,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,n]=em(r);e=e(void 0!==i?i:t.custom,s,n)}return e}let ey=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class eg{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:s,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tJ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=n;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!s,this.isControllingVariants=ed(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tX(e)&&e.set(a[t])}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eo.current||function(){if(eo.current=!0,es.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>en.current=t.matches;t.addEventListener("change",e),e()}else en.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||en.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),th(this.notifyUpdate),th(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=o.has(t);r&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t7){let e=t7[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):er()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ey.length;e++){let i=ey[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let s=e[r],n=i[r];if(tX(s))t.addValue(r,s);else if(tX(n))t.addValue(r,t9(s,{owner:t}));else if(n!==s)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(r);t.addValue(r,t9(void 0!==e?e:s,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t9(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(Z(i)||tx(i)))i=parseFloat(i);else{let r;r=i,!t4.find(H(r))&&tU.test(e)&&(i=t_(t,e))}this.setBaseTarget(t,tX(i)?i.get():i)}return tX(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=ef(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tX(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new t2),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t8.render(this.render)}}class ev extends eg{constructor(){super(...arguments),this.KeyframeResolver=tY}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tX(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eb=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ex={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ew=n.length;function eT(t,e,i){let{style:r,vars:s,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let i=e[t];if(o.has(t)){l=!0;continue}if(v(t)){s[t]=i;continue}{let e=eb(i,tq[t]);t.startsWith("origin")?(u=!0,a[t]=e):r[t]=e}}if(!e.transform&&(l||i?r.transform=function(t,e,i){let r="",s=!0;for(let o=0;o<ew;o++){let a=n[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let t=eb(l,tq[a]);if(!u){s=!1;let e=ex[a]||a;r+=`${e}(${t}) `}i&&(e[a]=t)}}return r=r.trim(),i?r=i(e,s?"":r):s&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;r.transformOrigin=`${t} ${e} ${i}`}}function ek(t,{style:e,vars:i},r,s){let n,o=t.style;for(n in e)o[n]=e[n];for(n in s?.applyProjectionStyles(o,r),i)o.setProperty(n,i[n])}let eS={};function eP(t,{layout:e,layoutId:i}){return o.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eS[t]||"opacity"===t)}function eA(t,e,i){let{style:r}=t,s={};for(let n in r)(tX(r[n])||e.style&&tX(e.style[n])||eP(n,t)||i?.getValue(n)?.liveStyle!==void 0)&&(s[n]=r[n]);return s}class eE extends ev{constructor(){super(...arguments),this.type="html",this.renderInstance=ek}readValueFromInstance(t,e){if(o.has(e))return this.projection?.isProjecting?m(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)})(t,e);{let i=window.getComputedStyle(t),r=(v(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return F(t,e)}build(t,e,i){eT(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eA(t,e,i)}}let eC=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eR={offset:"stroke-dashoffset",array:"stroke-dasharray"},eM={offset:"strokeDashoffset",array:"strokeDasharray"};function eD(t,{attrX:e,attrY:i,attrScale:r,pathLength:s,pathSpacing:n=1,pathOffset:o=0,...a},l,u,h){if(eT(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==r&&(c.scale=r),void 0!==s&&function(t,e,i=1,r=0,s=!0){t.pathLength=1;let n=s?eR:eM;t[n.offset]=Q.transform(-r);let o=Q.transform(e),a=Q.transform(i);t[n.array]=`${o} ${a}`}(c,s,n,o,!1)}let eV=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),ej=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eF(t,e,i){let r=eA(t,e,i);for(let i in t)(tX(t[i])||tX(e[i]))&&(r[-1!==n.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}class eO extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=er}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(o.has(e)){let t=tH(e);return t&&t.default||0}return e=eV.has(e)?e:eC(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eF(t,e,i)}build(t,e,i){eD(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in ek(t,e,void 0,r),e.attrs)t.setAttribute(eV.has(i)?i:eC(i),e.attrs[i])}mount(t){this.isSVGTag=ej(t.tagName),super.mount(t)}}let eL=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eI(t){if("string"!=typeof t||t.includes("-"));else if(eL.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eB=i(5155),eU=i(869);let ez=(0,s.createContext)({strict:!1});var eN=i(1508);let e$=(0,s.createContext)({});function eQ(t){return Array.isArray(t)?t.join(" "):t}let eW=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eq(t,e,i){for(let r in e)tX(e[r])||eP(r,i)||(t[r]=e[r])}let eG=()=>({...eW(),attrs:{}}),eH=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function e_(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eH.has(t)}let eK=t=>!e_(t);try{!function(t){"function"==typeof t&&(eK=e=>e.startsWith("on")?!e_(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}var eY=i(845),eX=i(2885);function eZ(t){return tX(t)?t.get():t}let eJ=t=>(e,i)=>{let r=(0,s.useContext)(e$),n=(0,s.useContext)(eY.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,s){return{latestValues:function(t,e,i,r){let s={},n=r(t,{});for(let t in n)s[t]=eZ(n[t]);let{initial:o,animate:a}=t,l=ed(t),u=ep(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,c=(h=h||!1===o)?a:o;if(c&&"boolean"!=typeof c&&!el(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let r=ef(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,r,s,t),renderState:e()}})(t,e,r,n);return i?o():(0,eX.M)(o)},e0=eJ({scrapeMotionValuesFromProps:eA,createRenderState:eW}),e1=eJ({scrapeMotionValuesFromProps:eF,createRenderState:eG}),e2=Symbol.for("motionComponentSymbol");function e5(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e3="data-"+eC("framerAppearId"),e9=(0,s.createContext)({});var e4=i(7494);function e8(t){var e,i;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;n&&function(t){for(let e in t)t7[e]={...t7[e],...t[e]}}(n);let a=eI(t)?e1:e0;function l(e,i){var n;let l,u={...(0,s.useContext)(eN.Q),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,s.useContext)(eU.L).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:h}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(ed(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eu(e)?e:void 0,animate:eu(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,s.useContext)(e$));return(0,s.useMemo)(()=>({initial:e,animate:i}),[eQ(e),eQ(i)])}(e),d=a(e,h);if(!h&&es.B){(0,s.useContext)(ez).strict;let e=function(t){let{drag:e,layout:i}=t7;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=e.MeasureLayout,c.visualElement=function(t,e,i,r,n){let{visualElement:o}=(0,s.useContext)(e$),a=(0,s.useContext)(ez),l=(0,s.useContext)(eY.t),u=(0,s.useContext)(eN.Q).reducedMotion,h=(0,s.useRef)(null);r=r||a.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let c=h.current,d=(0,s.useContext)(e9);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,r){let{layoutId:s,layout:n,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:n,alwaysMeasureLayout:!!o||a&&e5(a),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,n,d);let p=(0,s.useRef)(!1);(0,s.useInsertionEffect)(()=>{c&&p.current&&c.update(i,l)});let m=i[e3],f=(0,s.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return(0,e4.E)(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),f.current&&c.animationState&&c.animationState.animateChanges())}),(0,s.useEffect)(()=>{c&&(!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),c}(t,d,u,o,e.ProjectionNode)}return(0,eB.jsxs)(e$.Provider,{value:c,children:[l&&c.visualElement?(0,eB.jsx)(l,{visualElement:c.visualElement,...u}):null,function(t,e,i,{latestValues:r},n,o=!1){let a=(eI(t)?function(t,e,i,r){let n=(0,s.useMemo)(()=>{let i=eG();return eD(i,e,ej(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eq(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return eq(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,s.useMemo)(()=>{let i=eW();return eT(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(e,r,n,t),l=function(t,e,i){let r={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(eK(s)||!0===i&&e_(s)||!e&&!e_(s)||t.draggable&&s.startsWith("onDrag"))&&(r[s]=t[s]);return r}(e,"string"==typeof t,o),u=t!==s.Fragment?{...l,...a,ref:i}:{},{children:h}=e,c=(0,s.useMemo)(()=>tX(h)?h.get():h,[h]);return(0,s.createElement)(t,{...u,children:c})}(t,e,(n=c.visualElement,(0,s.useCallback)(t=>{t&&d.onMount&&d.onMount(t),n&&(t?n.mount(t):n.unmount()),i&&("function"==typeof i?i(t):e5(i)&&(i.current=t))},[n])),d,h,r)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let u=(0,s.forwardRef)(l);return u[e2]=t,u}function e6(t,e,i){let r=t.getProps();return ef(r,e,void 0!==i?i:r.custom,t)}function e7(t,e){return t?.[e]??t?.default??t}let it=t=>Array.isArray(t);function ie(t,e){let i=t.getValue("willChange");if(tX(i)&&i.add)return i.add(e);if(!i&&tn.WillChange){let i=new tn.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function ii(t){t.duration=0,t.type}let ir=(t,e)=>i=>e(t(i)),is=(...t)=>t.reduce(ir),io=t=>1e3*t,ia={layout:0,mainThread:0,waapi:0};function il(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function iu(t,e){return i=>i>0?e:t}let ih=(t,e,i)=>{let r=t*t,s=i*(e*e-r)+r;return s<0?0:Math.sqrt(s)},ic=[tC,tE,tR];function id(t){let e=ic.find(e=>e.test(t));if(Y(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tR&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let s=0,n=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;s=il(a,r,t+1/3),n=il(a,r,t),o=il(a,r,t-1/3)}else s=n=o=i;return{red:Math.round(255*s),green:Math.round(255*n),blue:Math.round(255*o),alpha:r}}(i)),i}let ip=(t,e)=>{let i=id(t),r=id(e);if(!i||!r)return iu(t,e);let s={...i};return t=>(s.red=ih(i.red,r.red,t),s.green=ih(i.green,r.green,t),s.blue=ih(i.blue,r.blue,t),s.alpha=k(i.alpha,r.alpha,t),tE.transform(s))},im=new Set(["none","hidden"]);function iy(t,e){return i=>k(t,e,i)}function ig(t){return"number"==typeof t?iy:"string"==typeof t?x(t)?iu:tM.test(t)?ip:ix:Array.isArray(t)?iv:"object"==typeof t?tM.test(t)?ip:ib:iu}function iv(t,e){let i=[...t],r=i.length,s=t.map((t,i)=>ig(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=s[e](t);return i}}function ib(t,e){let i={...t,...e},r={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(r[s]=ig(t[s])(t[s],e[s]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let ix=(t,e)=>{let i=tU.createTransformer(e),r=tO(t),s=tO(e);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?im.has(t)&&!s.values.length||im.has(e)&&!r.values.length?function(t,e){return im.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):is(iv(function(t,e){let i=[],r={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let n=e.types[s],o=t.indexes[n][r[n]],a=t.values[o]??0;i[s]=a,r[n]++}return i}(r,s),s.values),i):(Y(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),iu(t,e))};function iw(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?k(t,e,i):ig(t)(t,e)}let iT=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tu.update(e,t),stop:()=>th(e),now:()=>tc.isProcessing?tc.timestamp:tJ.now()}},ik=(t,e,i=10)=>{let r="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)r+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function iS(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iP(t,e,i){var r,s;let n=Math.max(e-5,0);return r=i-t(n),(s=e-n)?1e3/s*r:0}let iA={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iE(t,e){return t*Math.sqrt(1-e*e)}let iC=["duration","bounce"],iR=["stiffness","damping","mass"];function iM(t,e){return e.some(e=>void 0!==t[e])}function iD(t=iA.visualDuration,e=iA.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:n}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iA.velocity,stiffness:iA.stiffness,damping:iA.damping,mass:iA.mass,isResolvedFromDuration:!1,...t};if(!iM(t,iR)&&iM(t,iC))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,s=2*L(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:iA.mass,stiffness:r,damping:s}}else{let i=function({duration:t=iA.duration,bounce:e=iA.bounce,velocity:i=iA.velocity,mass:r=iA.mass}){let s,n;Y(t<=io(iA.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=L(iA.minDamping,iA.maxDamping,o),t=L(iA.minDuration,iA.maxDuration,t/1e3),o<1?(s=e=>{let r=e*o,s=r*t;return .001-(r-i)/iE(e,o)*Math.exp(-s)},n=e=>{let r=e*o*t,n=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=iE(Math.pow(e,2),o);return(r*i+i-n)*a*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(s,n,5/t);if(t=io(t),isNaN(a))return{stiffness:iA.stiffness,damping:iA.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:iA.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-((r.velocity||0)/1e3)}),f=p||0,y=h/(2*Math.sqrt(u*c)),g=a-o,v=Math.sqrt(u/c)/1e3,b=5>Math.abs(g);if(s||(s=b?iA.restSpeed.granular:iA.restSpeed.default),n||(n=b?iA.restDelta.granular:iA.restDelta.default),y<1){let t=iE(v,y);i=e=>a-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),r=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(r)+t*g*Math.cosh(r))/t}}let x={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let r=0===t?f:0;y<1&&(r=0===t?io(f):iP(i,t,e));let o=Math.abs(a-e)<=n;l.done=Math.abs(r)<=s&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(iS(x),2e4),e=ik(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function iV({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d,p=t[0],m={done:!1,value:p},f=i*e,y=p+f,g=void 0===o?y:o(y);g!==y&&(f=g-p);let v=t=>-f*Math.exp(-t/r),b=t=>g+v(t),x=t=>{let e=v(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?g:i},w=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var i;c=t,d=iD({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:iP(b,t,m.value),damping:s,stiffness:n,restDelta:u,restSpeed:h})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,x(t),w(t)),void 0!==c&&t>=c)?d.next(t-c):(e||x(t),m)}}}iD.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),s=Math.min(iS(r),2e4);return{type:"keyframes",ease:t=>r.next(s*t).value/e,duration:s/1e3}}(t,100,iD);return t.ease=e.ease,t.duration=io(e.duration),t.type="keyframes",t};let ij=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function iF(t,e,i,r){return t===e&&i===r?ts:s=>0===s||1===s?s:ij(function(t,e,i,r,s){let n,o,a=0;do(n=ij(o=e+(i-e)/2,r,s)-t)>0?i=o:e=o;while(Math.abs(n)>1e-7&&++a<12);return o}(s,0,1,t,i),e,r)}let iO=iF(.42,0,1,1),iL=iF(0,0,.58,1),iI=iF(.42,0,.58,1),iB=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iU=t=>e=>1-t(1-e),iz=iF(.33,1.53,.69,.99),iN=iU(iz),i$=iB(iN),iQ=t=>(t*=2)<1?.5*iN(t):.5*(2-Math.pow(2,-10*(t-1))),iW=t=>1-Math.sin(Math.acos(t)),iq=iU(iW),iG=iB(iW),iH=t=>Array.isArray(t)&&"number"==typeof t[0],i_={linear:ts,easeIn:iO,easeInOut:iI,easeOut:iL,circIn:iW,circInOut:iG,circOut:iq,backIn:iN,backInOut:i$,backOut:iz,anticipate:iQ},iK=t=>{if(iH(t)){X(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,r,s]=t;return iF(e,i,r,s)}return"string"==typeof t?(X(void 0!==i_[t],`Invalid easing type '${t}'`,"invalid-easing-type"),i_[t]):t},iY=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function iX({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var s;let n=Array.isArray(r)&&"number"!=typeof r[0]?r.map(iK):iK(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:s}={}){let n=t.length;if(X(n===e.length,"Both input and output ranges must be the same length","range-length"),1===n)return()=>e[0];if(2===n&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],s=i||tn.mix||iw,n=t.length-1;for(let i=0;i<n;i++){let n=s(t[i],t[i+1]);e&&(n=is(Array.isArray(e)?e[i]||ts:e,n)),r.push(n)}return r}(e,r,s),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let s=iY(t[r],t[r+1],i);return a[r](s)};return i?e=>u(L(t[0],t[n-1],e)):u}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let s=iY(0,e,r);t.push(k(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(n)?n:e.map(()=>n||iI).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let iZ=t=>null!==t;function iJ(t,{repeat:e,repeatType:i="loop"},r,s=1){let n=t.filter(iZ),o=s<0||e&&"loop"!==i&&e%2==1?0:n.length-1;return o&&void 0!==r?r:n[o]}let i0={decay:iV,inertia:iV,tween:iX,keyframes:iX,spring:iD};function i1(t){"string"==typeof t.type&&(t.type=i0[t.type])}class i2{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let i5=t=>t/100;class i3 extends i2{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tJ.now()&&this.tick(tJ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ia.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;i1(t);let{type:e=iX,repeat:i=0,repeatDelay:r=0,repeatType:s,velocity:n=0}=t,{keyframes:o}=t,a=e||iX;a!==iX&&"number"!=typeof o[0]&&(this.mixKeyframes=is(i5,iw(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=iS(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:s,mirroredGenerator:n,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>r;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/o)):"mirror"===c&&(b=n)),v=L(0,1,i)*o}let x=g?{done:!1,value:u[0]}:b.next(v);s&&(x.value=s(x.value));let{done:w}=x;g||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&p!==iV&&(x.value=iJ(u,this.options,f,this.speed)),m&&m(x.value),T&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=io(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tJ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=iT,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tJ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ia.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function i9(t){let e;return()=>(void 0===e&&(e=t()),e)}let i4=i9(()=>void 0!==window.ScrollTimeline),i8={},i6=function(t,e){let i=i9(t);return()=>i8[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),i7=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,rt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i7([0,.65,.55,1]),circOut:i7([.55,0,1,.45]),backIn:i7([.31,.01,.66,-.59]),backOut:i7([.33,1.53,.69,.99])};function re(t){return"function"==typeof t&&"applyToOptions"in t}class ri extends i2{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:s,allowFlatten:n=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!s,this.allowFlatten=n,this.options=t,X("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return re(t)&&i6()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:s=300,repeat:n=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?i6()?ik(e,i):"ease-out":iH(e)?i7(e):Array.isArray(e)?e.map(e=>t(e,i)||rt.easeOut):rt[e]}(a,s);Array.isArray(c)&&(h.easing=c),ta.value&&ia.waapi++;let d={delay:r,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let p=t.animate(h,d);return ta.value&&p.finished.finally(()=>{ia.waapi--}),p}(e,i,r,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=iJ(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=io(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&i4())?(this.animation.timeline=t,ts):e(this)}}let rr={anticipate:iQ,backInOut:i$,circInOut:iG};class rs extends ri{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in rr&&(t.ease=rr[t.ease])}(t),i1(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:s,...n}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new i3({...n,autoplay:!1}),a=io(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let rn=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tU.test(t)||"0"===t)&&!t.startsWith("url(")),ro=new Set(["opacity","clipPath","filter","transform"]),ra=i9(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class rl extends i2{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:n="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tJ.now();let c={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:s,repeatType:n,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||tb;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:s,type:n,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=tJ.now(),!function(t,e,i,r){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],o=rn(s,e),a=rn(n,e);return Y(o===a,`You are trying to animate ${e} from "${s}" to "${n}". "${o?n:s}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||re(i))&&r)}(t,s,n,o)&&((tn.instantAnimations||!a)&&u?.(iJ(t,i,e)),t[0]=t[t.length-1],ii(i),i.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:s,damping:n,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ra()&&i&&ro.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==s&&0!==n&&"inertia"!==o}(h)?new rs({...h,element:h.motionValue.owner.current}):new i3(h);c.finished.then(()=>this.notifyFinished()).catch(ts),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ty=!0,tv(),tg(),ty=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let ru=t=>null!==t,rh={type:"spring",stiffness:500,damping:25,restSpeed:10},rc={type:"keyframes",duration:.8},rd={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rp=(t,e,i,r={},s,n)=>a=>{let l=e7(r,t)||{},u=l.delay||r.delay||0,{elapsed:h=0}=r;h-=io(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:n?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:s,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(c,((t,{keyframes:e})=>e.length>2?rc:o.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:rh:rd)(t,c)),c.duration&&(c.duration=io(c.duration)),c.repeatDelay&&(c.repeatDelay=io(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(ii(c),0===c.delay&&(d=!0)),(tn.instantAnimations||tn.skipAnimations)&&(d=!0,ii(c),c.delay=0),c.allowFlatten=!l.type&&!l.ease,d&&!n&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let s=t.filter(ru),n=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[n]}(c.keyframes,l);if(void 0!==t)return void tu.update(()=>{c.onUpdate(t),c.onComplete()})}return l.isSync?new i3(c):new rl(c)};function rm(t,e,{delay:i=0,transitionOverride:r,type:s}={}){let{transition:n=t.getDefaultTransition(),transitionEnd:o,...a}=e;r&&(n=r);let l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(let e in a){let r=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||u&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(u,e))continue;let o={delay:i,...e7(n||{},e)},h=r.get();if(void 0!==h&&!r.isAnimating&&!Array.isArray(s)&&s===h&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[e3];if(i){let t=window.MotionHandoffAnimation(i,e,tu);null!==t&&(o.startTime=t,c=!0)}}ie(t,e),r.start(rp(e,r,s,t.shouldReduceMotion&&O.has(e)?{type:!1}:o,t,c));let d=r.animation;d&&l.push(d)}return o&&Promise.all(l).then(()=>{tu.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...s}=e6(t,e)||{};for(let e in s={...s,...i}){var n;let i=it(n=s[e])?n[n.length-1]||0:n;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t9(i))}}(t,o)})}),l}function rf(t,e,i={}){let r=e6(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let n=r?()=>Promise.all(rm(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,r=0,s=0,n=1,o){let a=[],l=t.variantChildren.size,u=(l-1)*s,h="function"==typeof r,c=h?t=>r(t,l):1===n?(t=0)=>t*s:(t=0)=>u-t*s;return Array.from(t.variantChildren).sort(ry).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(rf(t,e,{...o,delay:i+(h?0:r)+c(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,n,o,a,i)}:()=>Promise.resolve(),{when:a}=s;if(!a)return Promise.all([n(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[n,o]:[o,n];return t().then(()=>e())}}function ry(t,e){return t.sortNodePosition(e)}function rg(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}let rv=ec.length,rb=[...eh].reverse(),rx=eh.length;function rw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rT(){return{animate:rw(!0),whileInView:rw(),whileHover:rw(),whileTap:rw(),whileDrag:rw(),whileFocus:rw(),exit:rw()}}class rk{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rS extends rk{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>rf(t,e,i)));else if("string"==typeof e)r=rf(t,e,i);else{let s="function"==typeof e?e6(t,e,i.custom):e;r=Promise.all(rm(t,s,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=rT(),r=!0,s=e=>(i,r)=>{let s=e6(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...r}=s;i={...i,...r,...e}}return i};function n(n){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<rv;t++){let r=ec[t],s=e.props[r];(eu(s)||!1===s)&&(i[r]=s)}return i}(t.parent)||{},l=[],u=new Set,h={},c=1/0;for(let e=0;e<rx;e++){var d,p;let m=rb[e],f=i[m],y=void 0!==o[m]?o[m]:a[m],g=eu(y),v=m===n?f.isActive:null;!1===v&&(c=e);let b=y===a[m]&&y!==o[m]&&g;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...h},!f.isActive&&null===v||!y&&!f.prevProp||el(y)||"boolean"==typeof y)continue;let x=(d=f.prevProp,"string"==typeof(p=y)?p!==d:!!Array.isArray(p)&&!rg(p,d)),w=x||m===n&&f.isActive&&!b&&g||e>c&&g,T=!1,k=Array.isArray(y)?y:[y],S=k.reduce(s(m),{});!1===v&&(S={});let{prevResolvedValues:P={}}=f,A={...P,...S},E=e=>{w=!0,u.has(e)&&(T=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in A){let e=S[t],i=P[t];if(!h.hasOwnProperty(t))(it(e)&&it(i)?rg(e,i):e===i)?void 0!==e&&u.has(t)?E(t):f.protectedKeys[t]=!0:null!=e?E(t):u.add(t)}f.prevProp=y,f.prevResolvedValues=S,f.isActive&&(h={...h,...S}),r&&t.blockInitialAnimation&&(w=!1);let C=!(b&&x)||T;w&&C&&l.push(...k.map(t=>({animation:t,options:{type:m}})))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=e6(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let r=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=r??null}),l.push({animation:e})}let m=!!l.length;return r&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(m=!1),r=!1,m?e(l):Promise.resolve()}return{animateChanges:n,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let s=n(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=rT(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rP=0;class rA extends rk{constructor(){super(...arguments),this.id=rP++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let rE={x:!1,y:!1};function rC(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let rR=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function rM(t){return{point:{x:t.pageX,y:t.pageY}}}function rD(t,e,i,r){return rC(t,e,t=>rR(t)&&i(t,rM(t)),r)}function rV(t){return t.max-t.min}function rj(t,e,i,r=.5){t.origin=r,t.originPoint=k(e.min,e.max,t.origin),t.scale=rV(i)/rV(e),t.translate=k(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rF(t,e,i,r){rj(t.x,e.x,i.x,r?r.originX:void 0),rj(t.y,e.y,i.y,r?r.originY:void 0)}function rO(t,e,i){t.min=i.min+e.min,t.max=t.min+rV(e)}function rL(t,e,i){t.min=e.min-i.min,t.max=t.min+rV(e)}function rI(t,e,i){rL(t.x,e.x,i.x),rL(t.y,e.y,i.y)}function rB(t){return[t("x"),t("y")]}let rU=({current:t})=>t?t.ownerDocument.defaultView:null,rz=(t,e)=>Math.abs(t-e);class rN{constructor(t,e,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:s=!1,distanceThreshold:n=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rW(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(rz(t.x,e.x)**2+rz(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:r}=t,{timestamp:s}=tc;this.history.push({...r,timestamp:s});let{onStart:n,onMove:o}=this.handlers;e||(n&&n(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=r$(e,this.transformPagePoint),tu.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=rW("pointercancel"===t.type?this.lastMoveEventInfo:r$(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),r&&r(t,n)},!rR(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=n,this.contextWindow=r||window;let o=r$(rM(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=tc;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,rW(o,this.history)),this.removeListeners=is(rD(this.contextWindow,"pointermove",this.handlePointerMove),rD(this.contextWindow,"pointerup",this.handlePointerUp),rD(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),th(this.updatePoint)}}function r$(t,e){return e?{point:e(t.point)}:t}function rQ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rW({point:t},e){return{point:t,delta:rQ(t,rq(e)),offset:rQ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,s=rq(t);for(;i>=0&&(r=t[i],!(s.timestamp-r.timestamp>io(.1)));)i--;if(!r)return{x:0,y:0};let n=(s.timestamp-r.timestamp)/1e3;if(0===n)return{x:0,y:0};let o={x:(s.x-r.x)/n,y:(s.y-r.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function rq(t){return t[t.length-1]}function rG(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function rH(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function r_(t,e,i){return{min:rK(t,e),max:rK(t,i)}}function rK(t,e){return"number"==typeof t?t:t[e]||0}let rY=new WeakMap;class rX{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=er(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let s=t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(rM(t).point)},n=(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:s}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(rE[t])return null;else return rE[t]=!0,()=>{rE[t]=!1};return rE.x||rE.y?null:(rE.x=rE.y=!0,()=>{rE.x=rE.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rB(t=>{let e=this.getAxisMotionValue(t).get()||0;if($.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=rV(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&tu.postRender(()=>s(t,e)),ie(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},o=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:s,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),n&&n(t,e)},a=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>rB(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new rN(t,{onSessionStart:s,onStart:n,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:rU(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,r=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!r||!i)return;let{velocity:n}=r;this.startAnimation(n);let{onDragEnd:o}=this.getProps();o&&tu.postRender(()=>o(i,r))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!rZ(t,r,this.currentDirection))return;let s=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?k(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?k(i,t,r.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),s.set(n)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&e5(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:s}){return{x:rG(t.x,i,s),y:rG(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:r_(t,"left","right"),y:r_(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&rB(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e5(e))return!1;let r=e.current;X(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let n=function(t,e,i){let r=F(t,i),{scroll:s}=e;return s&&(D(r.x,s.offset.x),D(r.y,s.offset.y)),r}(r,s.root,this.visualElement.getTransformPagePoint()),o=(t=s.layout.layoutBox,{x:rH(t.x,n.x),y:rH(t.y,n.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=T(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:s,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rB(o=>{if(!rZ(o,e,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return ie(this.visualElement,t),i.start(rp(t,i,0,e,this.visualElement,!1))}stopAnimation(){rB(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rB(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){rB(e=>{let{drag:i}=this.getProps();if(!rZ(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,s=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:n}=r.layout.layoutBox[e];s.set(t[e]-k(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e5(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rB(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=rV(t),s=rV(e);return s>r?i=iY(e.min,e.max-r,t.min):r>s&&(i=iY(t.min,t.max-s,e.min)),L(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rB(e=>{if(!rZ(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:n}=this.constraints[e];i.set(k(s,n,r[e]))})}addListeners(){if(!this.visualElement.current)return;rY.set(this.visualElement,this);let t=rD(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e5(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),tu.read(e);let s=rC(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(rB(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),r(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:s=!1,dragElastic:n=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:s,dragElastic:n,dragMomentum:o}}}function rZ(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rJ extends rk{constructor(t){super(t),this.removeGroupControls=ts,this.removeListeners=ts,this.controls=new rX(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ts}unmount(){this.removeGroupControls(),this.removeListeners()}}let r0=t=>(e,i)=>{t&&tu.postRender(()=>t(e,i))};class r1 extends rk{constructor(){super(...arguments),this.removePointerDownListener=ts}onPointerDown(t){this.session=new rN(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rU(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:r0(t),onStart:r0(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&tu.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=rD(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var r2=i(2082);let r5={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r3(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let r9={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!Q.test(t))return t;else t=parseFloat(t);let i=r3(t,e.target.x),r=r3(t,e.target.y);return`${i}% ${r}%`}},r4=!1;class r8 extends s.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:s}=t;for(let t in r7)eS[t]=r7[t],v(t)&&(eS[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&r&&i.register(s),r4&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),r5.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:s}=this.props,{projection:n}=i;return n&&(n.isPresent=s,r4=!0,r||t.layoutDependency!==e||void 0===e||t.isPresent!==s?n.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?n.promote():n.relegate()||tu.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function r6(t){let[e,i]=(0,r2.xQ)(),r=(0,s.useContext)(eU.L);return(0,eB.jsx)(r8,{...t,layoutGroup:r,switchLayoutGroup:(0,s.useContext)(e9),isPresent:e,safeToRemove:i})}let r7={borderRadius:{...r9,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r9,borderTopRightRadius:r9,borderBottomLeftRadius:r9,borderBottomRightRadius:r9,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tU.parse(t);if(r.length>5)return t;let s=tU.createTransformer(t),n=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+n]/=o,r[1+n]/=a;let l=k(o,a,.5);return"number"==typeof r[2+n]&&(r[2+n]/=l),"number"==typeof r[3+n]&&(r[3+n]/=l),s(r)}}};var st=i(6983);function se(t){return(0,st.G)(t)&&"ownerSVGElement"in t}let si=(t,e)=>t.depth-e.depth;class sr{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(si),this.isDirty=!1,this.children.forEach(t)}}let ss=["TopLeft","TopRight","BottomLeft","BottomRight"],sn=ss.length,so=t=>"string"==typeof t?parseFloat(t):t,sa=t=>"number"==typeof t||Q.test(t);function sl(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let su=sc(0,.5,iq),sh=sc(.5,.95,ts);function sc(t,e,i){return r=>r<t?0:r>e?1:i(iY(t,e,r))}function sd(t,e){t.min=e.min,t.max=e.max}function sp(t,e){sd(t.x,e.x),sd(t.y,e.y)}function sm(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sf(t,e,i,r,s){return t-=e,t=r+1/i*(t-r),void 0!==s&&(t=r+1/s*(t-r)),t}function sy(t,e,[i,r,s],n,o){!function(t,e=0,i=1,r=.5,s,n=t,o=t){if($.test(e)&&(e=parseFloat(e),e=k(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=k(n.min,n.max,r);t===n&&(a-=e),t.min=sf(t.min,e,i,a,s),t.max=sf(t.max,e,i,a,s)}(t,e[i],e[r],e[s],e.scale,n,o)}let sg=["x","scaleX","originX"],sv=["y","scaleY","originY"];function sb(t,e,i,r){sy(t.x,e,sg,i?i.x:void 0,r?r.x:void 0),sy(t.y,e,sv,i?i.y:void 0,r?r.y:void 0)}function sx(t){return 0===t.translate&&1===t.scale}function sw(t){return sx(t.x)&&sx(t.y)}function sT(t,e){return t.min===e.min&&t.max===e.max}function sk(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sS(t,e){return sk(t.x,e.x)&&sk(t.y,e.y)}function sP(t){return rV(t.x)/rV(t.y)}function sA(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sE{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sC={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sR=["","X","Y","Z"],sM=0;function sD(t,e,i,r){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),r&&(r[t]=0))}function sV({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=sM++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ta.value&&(sC.nodes=sC.calculatedTargetDeltas=sC.calculatedProjections=0),this.nodes.forEach(sO),this.nodes.forEach(s$),this.nodes.forEach(sQ),this.nodes.forEach(sL),ta.addProjectionMetrics&&ta.addProjectionMetrics(sC)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sr)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t2),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=se(e)&&!(se(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,s=()=>this.root.updateBlockedByResize=!1;tu.read(()=>{r=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==r&&(r=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tJ.now(),r=({timestamp:e})=>{let s=e-i;s>=250&&(th(r),t(s-250))};return tu.setup(r,!0),()=>th(r)}(s,250),r5.hasAnimatedSinceResize&&(r5.hasAnimatedSinceResize=!1,this.nodes.forEach(sN)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||sK,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!sS(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e7(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||sN(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),th(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sW),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[e3];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",tu,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sB);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sU);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(sz),this.nodes.forEach(sj),this.nodes.forEach(sF)):this.nodes.forEach(sU),this.clearAllSnapshots();let t=tJ.now();tc.delta=L(0,1e3/60,t-tc.timestamp),tc.timestamp=t,tc.isProcessing=!0,td.update.process(tc),td.preRender.process(tc),td.render.process(tc),tc.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sI),this.sharedNodes.forEach(sq)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rV(this.snapshot.measuredBox.x)||rV(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=er(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sw(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||A(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),sZ((e=r).x),sZ(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return er();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(s0))){let{scroll:t}=this.root;t&&(D(e.x,t.offset.x),D(e.y,t.offset.y))}return e}removeElementScroll(t){let e=er();if(sp(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:s,options:n}=r;r!==this.root&&s&&n.layoutScroll&&(s.wasRoot&&sp(e,t),D(e.x,s.offset.x),D(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=er();sp(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&j(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),A(r.latestValues)&&j(i,r.latestValues)}return A(this.latestValues)&&j(i,this.latestValues),i}removeTransform(t){let e=er();sp(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!A(i.latestValues))continue;P(i.latestValues)&&i.updateSnapshot();let r=er();sp(r,i.measurePageBox()),sb(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return A(this.latestValues)&&sb(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tc.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:s}=this.options;if(this.layout&&(r||s)){if(this.resolvedRelativeTargetAt=tc.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rI(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sp(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=er(),this.targetWithTransforms=er()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,o,a;this.forceRelativeParentToResolveTarget(),n=this.target,o=this.relativeTarget,a=this.relativeParent.target,rO(n.x,o.x,a.x),rO(n.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sp(this.target,this.layout.layoutBox),M(this.target,this.targetDelta)):sp(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rI(this.relativeTargetOrigin,this.target,t.target),sp(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ta.value&&sC.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||P(this.parent.latestValues)||E(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tc.timestamp&&(i=!1),i)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;sp(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let s,n,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){n=(s=i[a]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&j(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,M(t,n)),r&&A(s.latestValues)&&j(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=er());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sm(this.prevProjectionDelta.x,this.projectionDelta.x),sm(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rF(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===n&&this.treeScale.y===o&&sA(this.projectionDelta.x,this.prevProjectionDelta.x)&&sA(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ta.value&&sC.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,s=r?r.latestValues:{},n={...this.latestValues},o=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=er(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(s_));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(sG(o.x,t.x,r),sG(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m,f,y;rI(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=r,sH(p.x,m.x,f.x,y),sH(p.y,m.y,f.y,y),i&&(u=this.relativeTarget,d=i,sT(u.x,d.x)&&sT(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=er()),sp(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,r,s,n){s?(t.opacity=k(0,i.opacity??1,su(r)),t.opacityExit=k(e.opacity??1,0,sh(r))):n&&(t.opacity=k(e.opacity??1,i.opacity??1,r));for(let s=0;s<sn;s++){let n=`border${ss[s]}Radius`,o=sl(e,n),a=sl(i,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sa(o)===sa(a)?(t[n]=Math.max(k(so(o),so(a),r),0),($.test(a)||$.test(o))&&(t[n]+="%")):t[n]=a)}(e.rotate||i.rotate)&&(t.rotate=k(e.rotate||0,i.rotate||0,r))}(n,s,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(th(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tu.update(()=>{r5.hasAnimatedSinceResize=!0,ia.layout++,this.motionValue||(this.motionValue=t9(0)),this.currentAnimation=function(t,e,i){let r=tX(t)?t:t9(t);return r.start(rp("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ia.layout--},onComplete:()=>{ia.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:s}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&sJ(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||er();let e=rV(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=rV(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}sp(e,i),j(e,s),rF(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sE),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&sD("z",t,r,this.animationValues);for(let e=0;e<sR.length;e++)sD(`rotate${sR[e]}`,t,r,this.animationValues),sD(`skew${sR[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eZ(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eZ(e?.pointerEvents)||""),this.hasProjected&&!A(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=r.animationValues||r.latestValues;this.applyTransformsToTarget();let n=function(t,e,i){let r="",s=t.x.translate/e.x,n=t.y.translate/e.y,o=i?.z||0;if((s||n||o)&&(r=`translate3d(${s}px, ${n}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:n,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),s&&(r+=`rotateX(${s}deg) `),n&&(r+=`rotateY(${n}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(n=i(s,n)),t.transform=n;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=r===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,eS){if(void 0===s[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=eS[e],l="none"===n?s[e]:i(s[e],r);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=r===this?eZ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(sB),this.root.sharedNodes.clear()}}}function sj(t){t.updateLayout()}function sF(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:s}=t.options,n=e.source!==t.layout.source;"size"===s?rB(t=>{let r=n?e.measuredBox[t]:e.layoutBox[t],s=rV(r);r.min=i[t].min,r.max=r.min+s}):sJ(s,e.layoutBox,i)&&rB(r=>{let s=n?e.measuredBox[r]:e.layoutBox[r],o=rV(i[r]);s.max=s.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=ee();rF(o,i,e.layoutBox);let a=ee();n?rF(a,t.applyTransform(r,!0),e.measuredBox):rF(a,i,e.layoutBox);let l=!sw(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:s,layout:n}=r;if(s&&n){let o=er();rI(o,e.layoutBox,s.layoutBox);let a=er();rI(a,i,n.layoutBox),sS(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sO(t){ta.value&&sC.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sL(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sI(t){t.clearSnapshot()}function sB(t){t.clearMeasurements()}function sU(t){t.isLayoutDirty=!1}function sz(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sN(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s$(t){t.resolveTargetDelta()}function sQ(t){t.calcProjection()}function sW(t){t.resetSkewAndRotation()}function sq(t){t.removeLeadSnapshot()}function sG(t,e,i){t.translate=k(e.translate,0,i),t.scale=k(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sH(t,e,i,r){t.min=k(e.min,i.min,r),t.max=k(e.max,i.max,r)}function s_(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let sK={duration:.45,ease:[.4,0,.1,1]},sY=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),sX=sY("applewebkit/")&&!sY("chrome/")?Math.round:ts;function sZ(t){t.min=sX(t.min),t.max=sX(t.max)}function sJ(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sP(e)-sP(i)))}function s0(t){return t!==t.root&&t.scroll?.wasRoot}let s1=sV({attachResizeListener:(t,e)=>rC(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),s2={current:void 0},s5=sV({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!s2.current){let t=new s1({});t.mount(window),t.setOptions({layoutScroll:!0}),s2.current=t}return s2.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function s3(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function s9(t){return!("touch"===t.pointerType||rE.x||rE.y)}function s4(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=r["onHover"+i];s&&tu.postRender(()=>s(e,rM(e)))}class s8 extends rk{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,s,n]=s3(t,i),o=t=>{if(!s9(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let n=t=>{s9(t)&&(r(t),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,s)};return r.forEach(t=>{t.addEventListener("pointerenter",o,s)}),n}(t,(t,e)=>(s4(this.node,e,"Start"),t=>s4(this.node,t,"End"))))}unmount(){}}class s6 extends rk{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=is(rC(this.node.current,"focus",()=>this.onFocus()),rC(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var s7=i(7351);let nt=(t,e)=>!!e&&(t===e||nt(t,e.parentElement)),ne=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ni=new WeakSet;function nr(t){return e=>{"Enter"===e.key&&t(e)}}function ns(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function nn(t){return rR(t)&&!(rE.x||rE.y)}function no(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=r["onTap"+("End"===i?"":i)];s&&tu.postRender(()=>s(e,rM(e)))}class na extends rk{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,s,n]=s3(t,i),o=t=>{let r=t.currentTarget;if(!nn(t))return;ni.add(r);let n=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ni.has(r)&&ni.delete(r),nn(t)&&"function"==typeof n&&n(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||nt(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,s),(0,s7.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let r=nr(()=>{if(ni.has(i))return;ns(i,"down");let t=nr(()=>{ns(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ns(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)})(t,s)),ne.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),n}(t,(t,e)=>(no(this.node,e,"Start"),(t,{success:e})=>no(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nl=new WeakMap,nu=new WeakMap,nh=t=>{let e=nl.get(t.target);e&&e(t)},nc=t=>{t.forEach(nh)},nd={some:0,all:1};class np extends rk{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:s}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nd[r]},o=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=e?i:r;n&&n(t)};var a=this.node.current;let l=function({root:t,...e}){let i=t||document;nu.has(i)||nu.set(i,{});let r=nu.get(i),s=JSON.stringify(e);return r[s]||(r[s]=new IntersectionObserver(nc,{root:t,...e})),r[s]}(n);return nl.set(a,o),l.observe(a),()=>{nl.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nm=function(t,e){if("undefined"==typeof Proxy)return e8;let i=new Map,r=(i,r)=>e8(i,r,t,e);return new Proxy((t,e)=>r(t,e),{get:(s,n)=>"create"===n?r:(i.has(n)||i.set(n,e8(n,void 0,t,e)),i.get(n))})}({animation:{Feature:rS},exit:{Feature:rA},inView:{Feature:np},tap:{Feature:na},focus:{Feature:s6},hover:{Feature:s8},pan:{Feature:r1},drag:{Feature:rJ,ProjectionNode:s5,MeasureLayout:r6},layout:{ProjectionNode:s5,MeasureLayout:r6}},(t,e)=>eI(t)?new eO(e):new eE(e,{allowProjection:t!==s.Fragment}))},3504:(t,e,i)=>{i.d(e,{T:()=>r});function r(){let t,e,i=new Promise((i,r)=>{t=i,e=r});function r(t){Object.assign(i,t),delete i.resolve,delete i.reject}return i.status="pending",i.catch(()=>{}),i.resolve=e=>{r({status:"fulfilled",value:e}),t(e)},i.reject=t=>{r({status:"rejected",reason:t}),e(t)},i}},5910:(t,e,i)=>{i.d(e,{Q:()=>r});var r=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},6101:(t,e,i)=>{i.d(e,{s:()=>o,t:()=>n});var r=i(2115);function s(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function n(...t){return e=>{let i=!1,r=t.map(t=>{let r=s(t,e);return i||"function"!=typeof r||(i=!0),r});if(i)return()=>{for(let e=0;e<r.length;e++){let i=r[e];"function"==typeof i?i():s(t[e],null)}}}}function o(...t){return r.useCallback(n(...t),t)}},6715:(t,e,i)=>{i.d(e,{Ht:()=>a,jE:()=>o});var r=i(2115),s=i(5155),n=r.createContext(void 0),o=t=>{let e=r.useContext(n);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},a=t=>{let{client:e,children:i}=t;return r.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,s.jsx)(n.Provider,{value:e,children:i})}},6784:(t,e,i)=>{i.d(e,{II:()=>c,v_:()=>l,wm:()=>h});var r=i(920),s=i(1239),n=i(3504),o=i(2020);function a(t){return Math.min(1e3*2**t,3e4)}function l(t){return(t??"online")!=="online"||s.t.isOnline()}var u=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function h(t){return t instanceof u}function c(t){let e,i=!1,h=0,c=!1,d=(0,n.T)(),p=()=>r.m.isFocused()&&("always"===t.networkMode||s.t.isOnline())&&t.canRun(),m=()=>l(t.networkMode)&&t.canRun(),f=i=>{c||(c=!0,t.onSuccess?.(i),e?.(),d.resolve(i))},y=i=>{c||(c=!0,t.onError?.(i),e?.(),d.reject(i))},g=()=>new Promise(i=>{e=t=>{(c||p())&&i(t)},t.onPause?.()}).then(()=>{e=void 0,c||t.onContinue?.()}),v=()=>{let e;if(c)return;let r=0===h?t.initialPromise:void 0;try{e=r??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(f).catch(e=>{if(c)return;let r=t.retry??3*!o.S$,s=t.retryDelay??a,n="function"==typeof s?s(h,e):s,l=!0===r||"number"==typeof r&&h<r||"function"==typeof r&&r(h,e);if(i||!l)return void y(e);h++,t.onFail?.(h,e),(0,o.yy)(n).then(()=>p()?void 0:g()).then(()=>{i?y(e):v()})})};return{promise:d,cancel:e=>{c||(y(new u(e)),t.abort?.())},continue:()=>(e?.(),d),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1},canStart:m,start:()=>(m()?v():g().then(v),d)}}},6983:(t,e,i)=>{i.d(e,{G:()=>r});function r(t){return"object"==typeof t&&null!==t}},7165:(t,e,i)=>{i.d(e,{jG:()=>s});var r=t=>setTimeout(t,0),s=function(){let t=[],e=0,i=t=>{t()},s=t=>{t()},n=r,o=r=>{e?t.push(r):n(()=>{i(r)})};return{batch:r=>{let o;e++;try{o=r()}finally{--e||(()=>{let e=t;t=[],e.length&&n(()=>{s(()=>{e.forEach(t=>{i(t)})})})})()}return o},batchCalls:t=>(...e)=>{o(()=>{t(...e)})},schedule:o,setNotifyFunction:t=>{i=t},setBatchNotifyFunction:t=>{s=t},setScheduler:t=>{n=t}}}()},7351:(t,e,i)=>{i.d(e,{s:()=>s});var r=i(6983);function s(t){return(0,r.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>s});var r=i(2115);let s=i(8972).B?r.useLayoutEffect:r.useEffect},7948:(t,e,i)=>{i.d(e,{k:()=>s});var r=i(2020),s=class{#C;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,r.gn)(this.gcTime)&&(this.#C=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(r.S$?1/0:3e5))}clearGcTimeout(){this.#C&&(clearTimeout(this.#C),this.#C=void 0)}}},8883:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8972:(t,e,i)=>{i.d(e,{B:()=>r});let r="undefined"!=typeof window},9688:(t,e,i)=>{i.d(e,{QP:()=>tt});let r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],s=e.nextPart.get(i),n=s?r(t.slice(1),s):void 0;if(n)return n;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,n=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:o(e,t)).classGroupId=i;return}if("function"==typeof t)return a(t)?void n(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,s])=>{n(s,o(e,t),i,r)})})},o=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},a=t=>t.isThemeGetter,l=/\s+/;function u(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=h(t))&&(r&&(r+=" "),r+=e);return r}let h=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=h(t[r]))&&(i&&(i+=" "),i+=e);return i},c=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},d=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,y=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,g=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,v=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=t=>m.test(t),w=t=>!!t&&!Number.isNaN(Number(t)),T=t=>!!t&&Number.isInteger(Number(t)),k=t=>t.endsWith("%")&&w(t.slice(0,-1)),S=t=>f.test(t),P=()=>!0,A=t=>y.test(t)&&!g.test(t),E=()=>!1,C=t=>v.test(t),R=t=>b.test(t),M=t=>!V(t)&&!B(t),D=t=>q(t,K,E),V=t=>d.test(t),j=t=>q(t,Y,A),F=t=>q(t,X,w),O=t=>q(t,H,E),L=t=>q(t,_,R),I=t=>q(t,J,C),B=t=>p.test(t),U=t=>G(t,Y),z=t=>G(t,Z),N=t=>G(t,H),$=t=>G(t,K),Q=t=>G(t,_),W=t=>G(t,J,!0),q=(t,e,i)=>{let r=d.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},G=(t,e,i=!1)=>{let r=p.exec(t);return!!r&&(r[1]?e(r[1]):i)},H=t=>"position"===t||"percentage"===t,_=t=>"image"===t||"url"===t,K=t=>"length"===t||"size"===t||"bg-size"===t,Y=t=>"length"===t,X=t=>"number"===t,Z=t=>"family-name"===t,J=t=>"shadow"===t;Symbol.toStringTag;let tt=function(t,...e){let i,o,a,h=function(l){let u;return o=(i={cache:(t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,s=(s,n)=>{i.set(s,n),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(s(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):s(t,e)}}})((u=e.reduce((t,e)=>e(t),t())).cacheSize),parseClassName:(t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e,i,r=[],s=0,n=0,o=0;for(let i=0;i<t.length;i++){let a=t[i];if(0===s&&0===n){if(":"===a){r.push(t.slice(o,i)),o=i+1;continue}if("/"===a){e=i;continue}}"["===a?s++:"]"===a?s--:"("===a?n++:")"===a&&n--}let a=0===r.length?t:t.substring(o),l=(i=a).endsWith("!")?i.substring(0,i.length-1):i.startsWith("!")?i.substring(1):i;return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:e&&e>o?e-o:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r})(u),sortModifiers:(t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}})(u),...(t=>{let e=(t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)n(i[t],r,t,e);return r})(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||(t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}})(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&o[t]?[...r,...o[t]]:r}}})(u)}).cache.get,a=i.cache.set,h=c,c(l)};function c(t){let e=o(t);if(e)return e;let r=((t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:n}=e,o=[],a=t.trim().split(l),u="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:l,modifiers:h,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:p}=i(e);if(l){u=e+(u.length>0?" "+u:u);continue}let m=!!p,f=r(m?d.substring(0,p):d);if(!f){if(!m||!(f=r(d))){u=e+(u.length>0?" "+u:u);continue}m=!1}let y=n(h).join(":"),g=c?y+"!":y,v=g+f;if(o.includes(v))continue;o.push(v);let b=s(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(g+e)}u=e+(u.length>0?" "+u:u)}return u})(t,i);return a(t,r),r}return function(){return h(u.apply(null,arguments))}}(()=>{let t=c("color"),e=c("font"),i=c("text"),r=c("font-weight"),s=c("tracking"),n=c("leading"),o=c("breakpoint"),a=c("container"),l=c("spacing"),u=c("radius"),h=c("shadow"),d=c("inset-shadow"),p=c("text-shadow"),m=c("drop-shadow"),f=c("blur"),y=c("perspective"),g=c("aspect"),v=c("ease"),b=c("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],E=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...E(),B,V],R=()=>["auto","hidden","clip","visible","scroll"],q=()=>["auto","contain","none"],G=()=>[B,V,l],H=()=>[x,"full","auto",...G()],_=()=>[T,"none","subgrid",B,V],K=()=>["auto",{span:["full",T,B,V]},T,B,V],Y=()=>[T,"auto",B,V],X=()=>["auto","min","max","fr",B,V],Z=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],J=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...G()],te=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],ti=()=>[t,B,V],tr=()=>[...E(),N,O,{position:[B,V]}],ts=()=>["no-repeat",{repeat:["","x","y","space","round"]}],tn=()=>["auto","cover","contain",$,D,{size:[B,V]}],to=()=>[k,U,j],ta=()=>["","none","full",u,B,V],tl=()=>["",w,U,j],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],tc=()=>[w,k,N,O],td=()=>["","none",f,B,V],tp=()=>["none",w,B,V],tm=()=>["none",w,B,V],tf=()=>[w,B,V],ty=()=>[x,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[S],breakpoint:[S],color:[P],container:[S],"drop-shadow":[S],ease:["in","out","in-out"],font:[M],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[S],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[S],shadow:[S],spacing:["px",w],text:[S],"text-shadow":[S],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,V,B,g]}],container:["container"],columns:[{columns:[w,V,B,a]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:q()}],"overscroll-x":[{"overscroll-x":q()}],"overscroll-y":[{"overscroll-y":q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:H()}],"inset-x":[{"inset-x":H()}],"inset-y":[{"inset-y":H()}],start:[{start:H()}],end:[{end:H()}],top:[{top:H()}],right:[{right:H()}],bottom:[{bottom:H()}],left:[{left:H()}],visibility:["visible","invisible","collapse"],z:[{z:[T,"auto",B,V]}],basis:[{basis:[x,"full","auto",a,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",V]}],grow:[{grow:["",w,B,V]}],shrink:[{shrink:["",w,B,V]}],order:[{order:[T,"first","last","none",B,V]}],"grid-cols":[{"grid-cols":_()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":_()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":X()}],"auto-rows":[{"auto-rows":X()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...Z(),"normal"]}],"justify-items":[{"justify-items":[...J(),"normal"]}],"justify-self":[{"justify-self":["auto",...J()]}],"align-content":[{content:["normal",...Z()]}],"align-items":[{items:[...J(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...J(),{baseline:["","last"]}]}],"place-content":[{"place-content":Z()}],"place-items":[{"place-items":[...J(),"baseline"]}],"place-self":[{"place-self":["auto",...J()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,U,j]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,B,F]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",k,V]}],"font-family":[{font:[z,V,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,B,V]}],"line-clamp":[{"line-clamp":[w,"none",B,F]}],leading:[{leading:[n,...G()]}],"list-image":[{"list-image":["none",B,V]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",B,V]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",B,j]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[w,"auto",B,V]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",B,V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",B,V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tr()}],"bg-repeat":[{bg:ts()}],"bg-size":[{bg:tn()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},T,B,V],radial:["",B,V],conic:[T,B,V]},Q,L]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,B,V]}],"outline-w":[{outline:["",w,U,j]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",h,W,I]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",d,W,I]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[w,j]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",p,W,I]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[w,B,V]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":tc()}],"mask-image-linear-to-pos":[{"mask-linear-to":tc()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":tc()}],"mask-image-t-to-pos":[{"mask-t-to":tc()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":tc()}],"mask-image-r-to-pos":[{"mask-r-to":tc()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":tc()}],"mask-image-b-to-pos":[{"mask-b-to":tc()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":tc()}],"mask-image-l-to-pos":[{"mask-l-to":tc()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":tc()}],"mask-image-x-to-pos":[{"mask-x-to":tc()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":tc()}],"mask-image-y-to-pos":[{"mask-y-to":tc()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[B,V]}],"mask-image-radial-from-pos":[{"mask-radial-from":tc()}],"mask-image-radial-to-pos":[{"mask-radial-to":tc()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":E()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":tc()}],"mask-image-conic-to-pos":[{"mask-conic-to":tc()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tr()}],"mask-repeat":[{mask:ts()}],"mask-size":[{mask:tn()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",B,V]}],filter:[{filter:["","none",B,V]}],blur:[{blur:td()}],brightness:[{brightness:[w,B,V]}],contrast:[{contrast:[w,B,V]}],"drop-shadow":[{"drop-shadow":["","none",m,W,I]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",w,B,V]}],"hue-rotate":[{"hue-rotate":[w,B,V]}],invert:[{invert:["",w,B,V]}],saturate:[{saturate:[w,B,V]}],sepia:[{sepia:["",w,B,V]}],"backdrop-filter":[{"backdrop-filter":["","none",B,V]}],"backdrop-blur":[{"backdrop-blur":td()}],"backdrop-brightness":[{"backdrop-brightness":[w,B,V]}],"backdrop-contrast":[{"backdrop-contrast":[w,B,V]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,B,V]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,B,V]}],"backdrop-invert":[{"backdrop-invert":["",w,B,V]}],"backdrop-opacity":[{"backdrop-opacity":[w,B,V]}],"backdrop-saturate":[{"backdrop-saturate":[w,B,V]}],"backdrop-sepia":[{"backdrop-sepia":["",w,B,V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",B,V]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",B,V]}],ease:[{ease:["linear","initial",v,B,V]}],delay:[{delay:[w,B,V]}],animate:[{animate:["none",b,B,V]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,B,V]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[B,V,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ty()}],"translate-x":[{"translate-x":ty()}],"translate-y":[{"translate-y":ty()}],"translate-z":[{"translate-z":ty()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",B,V]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",B,V]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[w,U,j,F]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(t,e,i)=>{i.d(e,{DX:()=>a,TL:()=>o});var r=i(2115),s=i(6101),n=i(5155);function o(t){let e=function(t){let e=r.forwardRef((t,e)=>{let{children:i,...n}=t;if(r.isValidElement(i)){var o;let t,a,l=(o=i,(a=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(a=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(t,e){let i={...e};for(let r in e){let s=t[r],n=e[r];/^on[A-Z]/.test(r)?s&&n?i[r]=(...t)=>{let e=n(...t);return s(...t),e}:s&&(i[r]=s):"style"===r?i[r]={...s,...n}:"className"===r&&(i[r]=[s,n].filter(Boolean).join(" "))}return{...t,...i}}(n,i.props);return i.type!==r.Fragment&&(u.ref=e?(0,s.t)(e,l):l),r.cloneElement(i,u)}return r.Children.count(i)>1?r.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=r.forwardRef((t,i)=>{let{children:s,...o}=t,a=r.Children.toArray(s),l=a.find(u);if(l){let t=l.props.children,s=a.map(e=>e!==l?e:r.Children.count(t)>1?r.Children.only(null):r.isValidElement(t)?t.props.children:null);return(0,n.jsx)(e,{...o,ref:i,children:r.isValidElement(t)?r.cloneElement(t,void 0,s):null})}return(0,n.jsx)(e,{...o,ref:i,children:s})});return i.displayName=`${t}.Slot`,i}var a=o("Slot"),l=Symbol("radix.slottable");function u(t){return r.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},9853:(t,e,i)=>{i.d(e,{X:()=>a,k:()=>l});var r=i(2020),s=i(7165),n=i(6784),o=i(7948),a=class extends o.k{#R;#M;#D;#s;#V;#j;#F;constructor(t){super(),this.#F=!1,this.#j=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#s=t.client,this.#D=this.#s.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#R=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,i=void 0!==e,r=i?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:i?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#R,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#V?.promise}setOptions(t){this.options={...this.#j,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#D.remove(this)}setData(t,e){let i=(0,r.pl)(this.state.data,t,this.options);return this.#O({data:i,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),i}setState(t,e){this.#O({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#V?.promise;return this.#V?.cancel(t),e?e.then(r.lQ).catch(r.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#R)}isActive(){return this.observers.some(t=>!1!==(0,r.Eh)(t.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===r.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===(0,r.d2)(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!(0,r.j3)(this.state.dataUpdatedAt,t))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#V?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#V?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#D.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#V&&(this.#F?this.#V.cancel({revert:!0}):this.#V.cancelRetry()),this.scheduleGc()),this.#D.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#O({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#V)return this.#V.continueRetry(),this.#V.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let i=new AbortController,s=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#F=!0,i.signal)})},o=()=>{let t=(0,r.ZM)(this.options,e),i=(()=>{let t={client:this.#s,queryKey:this.queryKey,meta:this.meta};return s(t),t})();return(this.#F=!1,this.options.persister)?this.options.persister(t,i,this):t(i)},a=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#s,state:this.state,fetchFn:o};return s(t),t})();this.options.behavior?.onFetch(a,this),this.#M=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#O({type:"fetch",meta:a.fetchOptions?.meta});let l=t=>{(0,n.wm)(t)&&t.silent||this.#O({type:"error",error:t}),(0,n.wm)(t)||(this.#D.config.onError?.(t,this),this.#D.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#V=(0,n.II)({initialPromise:e?.initialPromise,fn:a.fetchFn,abort:i.abort.bind(i),onSuccess:t=>{if(void 0===t)return void l(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){l(t);return}this.#D.config.onSuccess?.(t,this),this.#D.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:l,onFail:(t,e)=>{this.#O({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#O({type:"pause"})},onContinue:()=>{this.#O({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0}),this.#V.start()}#O(t){let e=e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...l(e.data,this.options),fetchMeta:t.meta??null};case"success":return this.#M=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=t.error;if((0,n.wm)(i)&&i.revert&&this.#M)return{...this.#M,fetchStatus:"idle"};return{...e,error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}};this.state=e(this.state),s.jG.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#D.notify({query:this,type:"updated",action:t})})}};function l(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.v_)(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}},9946:(t,e,i)=>{i.d(e,{A:()=>l});var r=i(2115);let s=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},n=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((t,e)=>{let{color:i="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:c,...d}=t;return(0,r.createElement)("svg",{ref:e,...o,width:s,height:s,stroke:i,strokeWidth:l?24*Number(a)/Number(s):a,className:n("lucide",u),...!h&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(d)&&{"aria-hidden":"true"},...d},[...c.map(t=>{let[e,i]=t;return(0,r.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let i=(0,r.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,r.createElement)(a,{ref:o,iconNode:e,className:n("lucide-".concat(s(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),l),...u})});return i.displayName=s(t),i}}}]);