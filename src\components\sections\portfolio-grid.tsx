'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import Link from 'next/link';
import { ArrowRight, ExternalLink, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { usePortfolio } from '@/lib/queries/hooks';
import { Loading } from '@/components/ui/loading';

const categories = [
  'All Projects',
  'Social Media Marketing',
  'SEO & Content Marketing',
  'Web Development',
  'Complete Digital Marketing',
  'Brand Identity',
  'E-commerce Development'
];

export default function PortfolioGrid() {
  const { data: portfolio, isLoading, error } = usePortfolio();
  const [selectedCategory, setSelectedCategory] = useState('All Projects');

  if (isLoading) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Loading size="lg" text="Loading portfolio..." />
          </div>
        </div>
      </section>
    );
  }

  if (error || !portfolio) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <p className="text-red-600">Failed to load portfolio. Please try again later.</p>
        </div>
      </section>
    );
  }

  const filteredPortfolio = selectedCategory === 'All Projects' 
    ? portfolio 
    : portfolio.filter(item => item.category === selectedCategory);

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Success
            <span className="block text-brand-navy">Stories</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore our diverse portfolio of successful digital marketing campaigns. 
            Each project showcases our expertise and commitment to delivering exceptional results.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          <div className="flex items-center mr-4 text-gray-600">
            <Filter className="h-4 w-4 mr-2" />
            <span className="text-sm font-medium">Filter by:</span>
          </div>
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className={selectedCategory === category 
                ? "bg-brand-navy hover:bg-brand-navy-dark" 
                : "border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
              }
            >
              {category}
            </Button>
          ))}
        </motion.div>

        {/* Portfolio Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPortfolio.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Card className="h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                {/* Project Image */}
                <div className="relative h-48 bg-gradient-to-br from-brand-navy to-brand-navy-dark overflow-hidden">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <h3 className="text-lg font-bold mb-2">{item.client}</h3>
                      <Badge className="bg-brand-gold text-gray-900">
                        {item.category}
                      </Badge>
                    </div>
                  </div>
                  
                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-brand-gold/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="text-center text-gray-900">
                      <ExternalLink className="h-8 w-8 mx-auto mb-2" />
                      <span className="font-semibold">View Details</span>
                    </div>
                  </div>
                </div>

                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-brand-navy transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed line-clamp-3">
                    {item.description}
                  </p>

                  {/* Key Results */}
                  <div className="space-y-3 mb-6">
                    {item.results.slice(0, 2).map((result, resultIndex) => (
                      <div key={resultIndex} className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">{result.metric}</span>
                        <div className="text-right">
                          <div className="text-lg font-bold text-brand-navy">{result.value}</div>
                          <div className="text-xs text-brand-gold">{result.improvement}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Technologies */}
                  <div className="mb-6">
                    <div className="flex flex-wrap gap-2">
                      {item.technologies.slice(0, 3).map((tech, techIndex) => (
                        <Badge key={techIndex} variant="secondary" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                      {item.technologies.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{item.technologies.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Project Duration */}
                  <div className="text-sm text-gray-500 mb-4">
                    Duration: {item.duration}
                  </div>

                  {/* CTA */}
                  <div className="flex space-x-3">
                    <Button 
                      asChild 
                      size="sm"
                      className="flex-1 bg-brand-navy hover:bg-brand-navy-dark text-white"
                    >
                      <Link href={`/portfolio/${item.id}`}>
                        View Case Study
                      </Link>
                    </Button>
                    
                    {item.url && (
                      <Button 
                        asChild 
                        variant="outline" 
                        size="sm"
                        className="border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
                      >
                        <a href={item.url} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Load More Button */}
        {filteredPortfolio.length > 6 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <Button 
              variant="outline" 
              size="lg"
              className="border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
            >
              Load More Projects
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  );
}
