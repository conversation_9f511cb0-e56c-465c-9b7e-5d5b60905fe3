{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_ae14c56d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_ae14c56d-module__NZbT2q__className\",\n  \"variable\": \"poppins_ae14c56d-module__NZbT2q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_ae14c56d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22],%22variable%22:%22--font-poppins%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/query-provider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/lib/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/query-provider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/query-provider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/scroll-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/scroll-to-top.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/scroll-to-top.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/scroll-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/scroll-to-top.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/scroll-to-top.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { <PERSON>, <PERSON><PERSON><PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\nimport QueryProvider from \"@/lib/query-provider\";\nimport Header from \"@/components/layout/header\";\nimport Footer from \"@/components/layout/footer\";\nimport ScrollToTop from \"@/components/ui/scroll-to-top\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nconst poppins = Poppins({\n  subsets: [\"latin\"],\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\"],\n  variable: \"--font-poppins\",\n});\n\nexport const viewport = {\n  width: 'device-width',\n  initialScale: 1,\n  themeColor: '#1e40af',\n};\n\nexport const metadata: Metadata = {\n  metadataBase: new URL('https://lunarcubes.com.np'),\n  title: \"Lunar Cubes - Digital Marketing Agency in Nepal | From Concept to Cosmos\",\n  description: \"Leading digital marketing agency in Nepal helping SMEs grow online. Expert services in social media marketing, SEO, web development, and Google Ads. Serving Kathman<PERSON>, Pokhara & beyond.\",\n  keywords: \"digital marketing Nepal, SEO Nepal, social media marketing Kathmandu, web development Nepal, Google Ads Nepal, digital agency Kathmandu\",\n  authors: [{ name: \"Lunar Cubes Digital Marketing\" }],\n  creator: \"Lunar Cubes\",\n  publisher: \"Lunar Cubes\",\n  robots: \"index, follow\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://lunarcubes.com.np\",\n    siteName: \"Lunar Cubes\",\n    title: \"Lunar Cubes - Digital Marketing Agency in Nepal\",\n    description: \"Leading digital marketing agency in Nepal helping SMEs grow online with expert services in social media marketing, SEO, web development, and Google Ads.\",\n    images: [\n      {\n        url: \"/images/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"Lunar Cubes Digital Marketing Agency Nepal\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    site: \"@lunarcubes\",\n    creator: \"@lunarcubes\",\n  },\n\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <body className={`${inter.variable} ${poppins.variable} font-sans antialiased`}>\n        <QueryProvider>\n          <div className=\"flex flex-col min-h-screen\">\n            <Header />\n            <main className=\"flex-1\">\n              {children}\n            </main>\n            <Footer />\n          </div>\n          <ScrollToTop />\n        </QueryProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AACA;AACA;AACA;;;;;;;;;AAaO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;IACd,YAAY;AACd;AAEO,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAgC;KAAE;IACpD,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,SAAS;IACX;AAEF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;sBAC5E,cAAA,8OAAC,gIAAA,CAAA,UAAa;;kCACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0CACP,8OAAC;gCAAK,WAAU;0CACb;;;;;;0CAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;kCAET,8OAAC,+IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}