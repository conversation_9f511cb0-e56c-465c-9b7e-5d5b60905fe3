[{"C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\portfolio\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\services\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\layout\\footer.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\layout\\header.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-hero.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-stats.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-story.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-values.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\contact-form.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\contact-hero.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\contact-info.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\hero.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\lead-capture.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\packages.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\portfolio-showcase.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-cta.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-grid.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-hero.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-overview.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\team-section.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\testimonials.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\why-choose-us.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\seo.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\avatar.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\badge.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\button.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\card.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\carousel.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\dialog.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\form.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\input.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\label.tsx": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\loading.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\navigation-menu.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\optimized-image.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\scroll-to-top.tsx": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\select.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\sheet.tsx": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\textarea.tsx": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\queries\\api.ts": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\queries\\hooks.ts": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\query-provider.tsx": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\scroll-utils.ts": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\utils.ts": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\types\\index.ts": "50"}, {"size": 1198, "mtime": 1753418588905, "results": "51", "hashOfConfig": "52"}, {"size": 1024, "mtime": 1753418931369, "results": "53", "hashOfConfig": "52"}, {"size": 2493, "mtime": 1753419191751, "results": "54", "hashOfConfig": "52"}, {"size": 3578, "mtime": 1753419346703, "results": "55", "hashOfConfig": "52"}, {"size": 665, "mtime": 1753418551761, "results": "56", "hashOfConfig": "52"}, {"size": 1066, "mtime": 1753419514191, "results": "57", "hashOfConfig": "52"}, {"size": 1089, "mtime": 1753418798141, "results": "58", "hashOfConfig": "52"}, {"size": 9830, "mtime": 1753418050342, "results": "59", "hashOfConfig": "52"}, {"size": 5775, "mtime": 1753417930658, "results": "60", "hashOfConfig": "52"}, {"size": 5072, "mtime": 1753418613920, "results": "61", "hashOfConfig": "52"}, {"size": 8611, "mtime": 1753418767869, "results": "62", "hashOfConfig": "52"}, {"size": 8013, "mtime": 1753418650897, "results": "63", "hashOfConfig": "52"}, {"size": 8516, "mtime": 1753418728448, "results": "64", "hashOfConfig": "52"}, {"size": 17803, "mtime": 1753419022830, "results": "65", "hashOfConfig": "52"}, {"size": 6921, "mtime": 1753418963894, "results": "66", "hashOfConfig": "52"}, {"size": 12901, "mtime": 1753419072915, "results": "67", "hashOfConfig": "52"}, {"size": 9459, "mtime": 1753418290183, "results": "68", "hashOfConfig": "52"}, {"size": 13411, "mtime": 1753418577684, "results": "69", "hashOfConfig": "52"}, {"size": 8858, "mtime": 1753418550258, "results": "70", "hashOfConfig": "52"}, {"size": 9996, "mtime": 1753418559570, "results": "71", "hashOfConfig": "52"}, {"size": 9051, "mtime": 1753418908154, "results": "72", "hashOfConfig": "52"}, {"size": 10352, "mtime": 1753418866698, "results": "73", "hashOfConfig": "52"}, {"size": 6032, "mtime": 1753418825866, "results": "74", "hashOfConfig": "52"}, {"size": 7332, "mtime": 1753418351696, "results": "75", "hashOfConfig": "52"}, {"size": 9429, "mtime": 1753418690482, "results": "76", "hashOfConfig": "52"}, {"size": 9518, "mtime": 1753418437731, "results": "77", "hashOfConfig": "52"}, {"size": 10911, "mtime": 1753418366777, "results": "78", "hashOfConfig": "52"}, {"size": 4824, "mtime": 1753417897893, "results": "79", "hashOfConfig": "52"}, {"size": 1097, "mtime": 1753417398362, "results": "80", "hashOfConfig": "52"}, {"size": 1631, "mtime": 1753417398353, "results": "81", "hashOfConfig": "52"}, {"size": 2123, "mtime": 1753417398230, "results": "82", "hashOfConfig": "52"}, {"size": 1989, "mtime": 1753417398258, "results": "83", "hashOfConfig": "52"}, {"size": 5556, "mtime": 1753417398387, "results": "84", "hashOfConfig": "52"}, {"size": 3982, "mtime": 1753417398396, "results": "85", "hashOfConfig": "52"}, {"size": 3759, "mtime": 1753417398323, "results": "86", "hashOfConfig": "52"}, {"size": 967, "mtime": 1753417398265, "results": "87", "hashOfConfig": "52"}, {"size": 611, "mtime": 1753417398328, "results": "88", "hashOfConfig": "52"}, {"size": 1922, "mtime": 1753417984340, "results": "89", "hashOfConfig": "52"}, {"size": 6664, "mtime": 1753417398420, "results": "90", "hashOfConfig": "52"}, {"size": 1829, "mtime": 1753419325456, "results": "91", "hashOfConfig": "52"}, {"size": 1406, "mtime": 1753419164123, "results": "92", "hashOfConfig": "52"}, {"size": 6253, "mtime": 1753417398346, "results": "93", "hashOfConfig": "52"}, {"size": 4090, "mtime": 1753417398407, "results": "94", "hashOfConfig": "52"}, {"size": 759, "mtime": 1753417398279, "results": "95", "hashOfConfig": "52"}, {"size": 5877, "mtime": 1753417806887, "results": "96", "hashOfConfig": "52"}, {"size": 5627, "mtime": 1753417855424, "results": "97", "hashOfConfig": "52"}, {"size": 1195, "mtime": 1753417866297, "results": "98", "hashOfConfig": "52"}, {"size": 1203, "mtime": 1753419151051, "results": "99", "hashOfConfig": "52"}, {"size": 166, "mtime": 1753417365797, "results": "100", "hashOfConfig": "52"}, {"size": 3081, "mtime": 1753417574663, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "igoco1", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\portfolio\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-stats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-story.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\about-values.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\contact-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\contact-info.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\lead-capture.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\packages.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\portfolio-showcase.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-cta.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-grid.tsx", ["252", "253", "254", "255"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\services-overview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\team-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\testimonials.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\sections\\why-choose-us.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\seo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\optimized-image.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\scroll-to-top.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\queries\\api.ts", ["256", "257"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\queries\\hooks.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\query-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\scroll-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\lunar\\src\\types\\index.ts", [], [], {"ruleId": "258", "severity": 1, "message": "259", "line": 7, "column": 29, "nodeType": null, "messageId": "260", "endLine": 7, "endColumn": 44}, {"ruleId": "258", "severity": 1, "message": "261", "line": 7, "column": 46, "nodeType": null, "messageId": "260", "endLine": 7, "endColumn": 56}, {"ruleId": "258", "severity": 1, "message": "262", "line": 7, "column": 58, "nodeType": null, "messageId": "260", "endLine": 7, "endColumn": 67}, {"ruleId": "258", "severity": 1, "message": "263", "line": 9, "column": 23, "nodeType": null, "messageId": "260", "endLine": 9, "endColumn": 46}, {"ruleId": "258", "severity": 1, "message": "264", "line": 14, "column": 7, "nodeType": null, "messageId": "260", "endLine": 14, "endColumn": 15}, {"ruleId": "265", "severity": 2, "message": "266", "line": 160, "column": 51, "nodeType": "267", "messageId": "268", "endLine": 160, "endColumn": 54, "suggestions": "269"}, "@typescript-eslint/no-unused-vars", "'CardDescription' is defined but never used.", "unusedVar", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'useCaseStudiesByService' is defined but never used.", "'API_BASE' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["270", "271"], {"messageId": "272", "fix": "273", "desc": "274"}, {"messageId": "275", "fix": "276", "desc": "277"}, "suggestUnknown", {"range": "278", "text": "279"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "280", "text": "281"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", [5100, 5103], "unknown", [5100, 5103], "never"]