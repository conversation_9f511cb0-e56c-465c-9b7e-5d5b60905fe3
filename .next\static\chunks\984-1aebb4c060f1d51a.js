"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[984],{285:(e,r,t)=>{t.d(r,{$:()=>d});var a=t(5155);t(2115);var n=t(9708),s=t(2085),i=t(9434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:s,asChild:d=!1,...c}=e,l=d?n.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:r})),...c})}},1500:(e,r,t)=>{t.d(r,{hz:()=>g,xi:()=>b,g9:()=>m,A$:()=>f,Q5:()=>y,kf:()=>p});var a=t(2960);let n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:500;return new Promise(r=>setTimeout(r,e))};async function s(e){await n();try{let r=await fetch(e);if(!r.ok)throw Error("HTTP error! status: ".concat(r.status));return await r.json()}catch(r){throw console.error("Error fetching ".concat(e,":"),r),Error("Failed to fetch data from ".concat(e))}}let i=async()=>s("/data/company.json"),o=async()=>s("/data/services.json"),d=async()=>s("/data/team.json"),c=async()=>s("/data/testimonials.json"),l=async()=>s("/data/portfolio.json"),u=async()=>s("/data/packages.json"),v={company:["company"],services:["services"],team:["team"],testimonials:["testimonials"],portfolio:["portfolio"],packages:["packages"]},g=()=>(0,a.I)({queryKey:v.company,queryFn:i,staleTime:36e5}),f=()=>(0,a.I)({queryKey:v.services,queryFn:o,staleTime:18e5}),y=()=>(0,a.I)({queryKey:v.team,queryFn:d,staleTime:36e5}),p=()=>(0,a.I)({queryKey:v.testimonials,queryFn:c,staleTime:9e5}),m=()=>(0,a.I)({queryKey:v.portfolio,queryFn:l,staleTime:18e5}),b=()=>(0,a.I)({queryKey:v.packages,queryFn:u,staleTime:18e5})},1539:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},6126:(e,r,t)=>{t.d(r,{E:()=>d});var a=t(5155);t(2115);var n=t(9708),s=t(2085),i=t(9434);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:t,asChild:s=!1,...d}=e,c=s?n.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),r),...d})}},6695:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>s,aR:()=>i});var a=t(5155);t(2115);var n=t(9434);function s(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...t})}},6785:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7023:(e,r,t)=>{t.d(r,{Rh:()=>i});var a=t(5155),n=t(3463),s=t(9434);function i(e){let{size:r="md",className:t,text:i}=e;return(0,a.jsxs)("div",{className:(0,s.cn)("flex flex-col items-center justify-center space-y-4",t),children:[(0,a.jsx)(n.P.div,{className:(0,s.cn)("border-4 border-gray-200 border-t-brand-navy rounded-full",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[r]),animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),i&&(0,a.jsx)(n.P.p,{className:"text-gray-600 text-sm",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:i})]})}},8564:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9434:(e,r,t)=>{t.d(r,{cn:()=>s});var a=t(2596),n=t(9688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}}}]);