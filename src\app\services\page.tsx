import { Metadata } from 'next';
import ServicesHero from '@/components/sections/services-hero';
import ServicesGrid from '@/components/sections/services-grid';
import ServicesCTA from '@/components/sections/services-cta';

export const metadata: Metadata = {
  title: 'Digital Marketing Services - Lunar Cubes Nepal',
  description: 'Comprehensive digital marketing services in Nepal. Social media marketing, SEO, web development, Google Ads, content marketing, and brand identity services for Nepali businesses.',
  keywords: 'digital marketing services Nepal, SEO services Kathmandu, social media marketing Nepal, web development Nepal, Google Ads Nepal, content marketing services',
  openGraph: {
    title: 'Digital Marketing Services - Lunar Cubes Nepal',
    description: 'Expert digital marketing services tailored for Nepali businesses. From social media to SEO, we help your business grow online.',
    url: '/services',
  },
};

export default function ServicesPage() {
  return (
    <div>
      <ServicesHero />
      <ServicesGrid />
      <ServicesCTA />
    </div>
  );
}
