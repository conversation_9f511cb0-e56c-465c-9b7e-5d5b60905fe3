"use strict";exports.id=366,exports.ids=[366],exports.modules={3589:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5336:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(a,b,c)=>{c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},16725:(a,b,c)=>{c.d(b,{UC:()=>db,In:()=>c9,q7:()=>dd,VF:()=>df,p4:()=>de,ZL:()=>da,bL:()=>c6,wn:()=>dh,PP:()=>dg,l9:()=>c7,WT:()=>c8,LM:()=>dc});var d,e,f,g=c(43210),h=c.t(g,2),i=c(51215);function j(a,[b,c]){return Math.min(c,Math.max(b,a))}function k(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}var l=c(60687);function m(a,b=[]){let c=[],d=()=>{let b=c.map(a=>g.createContext(a));return function(c){let d=c?.[a]||b;return g.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let e=g.createContext(d),f=c.length;c=[...c,d];let h=b=>{let{scope:c,children:d,...h}=b,i=c?.[a]?.[f]||e,j=g.useMemo(()=>h,Object.values(h));return(0,l.jsx)(i.Provider,{value:j,children:d})};return h.displayName=b+"Provider",[h,function(c,h){let i=h?.[a]?.[f]||e,j=g.useContext(i);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return g.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}var n=c(98599),o=c(8730),p=new WeakMap;function q(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=r(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function r(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],p.set(this,!0)}set(a,b){return p.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=r(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=q(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=q(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return q(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var s=g.createContext(void 0),t=c(14163);function u(a){let b=g.useRef(a);return g.useEffect(()=>{b.current=a}),g.useMemo(()=>(...a)=>b.current?.(...a),[])}var v="dismissableLayer.update",w=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=g.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:i,onDismiss:j,...m}=a,o=g.useContext(w),[p,q]=g.useState(null),r=p?.ownerDocument??globalThis?.document,[,s]=g.useState({}),x=(0,n.s)(b,a=>q(a)),A=Array.from(o.layers),[B]=[...o.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=p?A.indexOf(p):-1,E=o.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=u(a),d=g.useRef(!1),e=g.useRef(()=>{});return g.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){z("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...o.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),i?.(a),a.defaultPrevented||j?.())},r),H=function(a,b=globalThis?.document){let c=u(a),d=g.useRef(!1);return g.useEffect(()=>{let a=a=>{a.target&&!d.current&&z("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...o.branches].some(a=>a.contains(b))&&(h?.(a),i?.(a),a.defaultPrevented||j?.())},r);return!function(a,b=globalThis?.document){let c=u(a);g.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===o.layers.size-1&&(d?.(a),!a.defaultPrevented&&j&&(a.preventDefault(),j()))},r),g.useEffect(()=>{if(p)return c&&(0===o.layersWithOutsidePointerEventsDisabled.size&&(e=r.body.style.pointerEvents,r.body.style.pointerEvents="none"),o.layersWithOutsidePointerEventsDisabled.add(p)),o.layers.add(p),y(),()=>{c&&1===o.layersWithOutsidePointerEventsDisabled.size&&(r.body.style.pointerEvents=e)}},[p,r,c,o]),g.useEffect(()=>()=>{p&&(o.layers.delete(p),o.layersWithOutsidePointerEventsDisabled.delete(p),y())},[p,o]),g.useEffect(()=>{let a=()=>s({});return document.addEventListener(v,a),()=>document.removeEventListener(v,a)},[]),(0,l.jsx)(t.sG.div,{...m,ref:x,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:k(a.onFocusCapture,H.onFocusCapture),onBlurCapture:k(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:k(a.onPointerDownCapture,G.onPointerDownCapture)})});function y(){let a=new CustomEvent(v);document.dispatchEvent(a)}function z(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,t.hO)(e,f):e.dispatchEvent(f)}x.displayName="DismissableLayer",g.forwardRef((a,b)=>{let c=g.useContext(w),d=g.useRef(null),e=(0,n.s)(b,d);return g.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,l.jsx)(t.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var A=0;function B(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var C="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",E={bubbles:!1,cancelable:!0},F=g.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[i,j]=g.useState(null),k=u(e),m=u(f),o=g.useRef(null),p=(0,n.s)(b,a=>j(a)),q=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(d){let a=function(a){if(q.paused||!i)return;let b=a.target;i.contains(b)?o.current=b:I(o.current,{select:!0})},b=function(a){if(q.paused||!i)return;let b=a.relatedTarget;null!==b&&(i.contains(b)||I(o.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&I(i)});return i&&c.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,i,q.paused]),g.useEffect(()=>{if(i){J.add(q);let a=document.activeElement;if(!i.contains(a)){let b=new CustomEvent(C,E);i.addEventListener(C,k),i.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(I(d,{select:b}),document.activeElement!==c)return}(G(i).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&I(i))}return()=>{i.removeEventListener(C,k),setTimeout(()=>{let b=new CustomEvent(D,E);i.addEventListener(D,m),i.dispatchEvent(b),b.defaultPrevented||I(a??document.body,{select:!0}),i.removeEventListener(D,m),J.remove(q)},0)}}},[i,k,m,q]);let r=g.useCallback(a=>{if(!c&&!d||q.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=G(a);return[H(b,a),H(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&I(f,{select:!0})):(a.preventDefault(),c&&I(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,q.paused]);return(0,l.jsx)(t.sG.div,{tabIndex:-1,...h,ref:p,onKeyDown:r})});function G(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function H(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function I(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}F.displayName="FocusScope";var J=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=K(a,b)).unshift(b)},remove(b){a=K(a,b),a[0]?.resume()}}}();function K(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var L=globalThis?.document?g.useLayoutEffect:()=>{},M=h[" useId ".trim().toString()]||(()=>void 0),N=0;function O(a){let[b,c]=g.useState(M());return L(()=>{a||c(a=>a??String(N++))},[a]),a||(b?`radix-${b}`:"")}let P=["top","right","bottom","left"],Q=Math.min,R=Math.max,S=Math.round,T=Math.floor,U=a=>({x:a,y:a}),V={left:"right",right:"left",bottom:"top",top:"bottom"},W={start:"end",end:"start"};function X(a,b){return"function"==typeof a?a(b):a}function Y(a){return a.split("-")[0]}function Z(a){return a.split("-")[1]}function $(a){return"x"===a?"y":"x"}function _(a){return"y"===a?"height":"width"}let aa=new Set(["top","bottom"]);function ab(a){return aa.has(Y(a))?"y":"x"}function ac(a){return a.replace(/start|end/g,a=>W[a])}let ad=["left","right"],ae=["right","left"],af=["top","bottom"],ag=["bottom","top"];function ah(a){return a.replace(/left|right|bottom|top/g,a=>V[a])}function ai(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function aj(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function ak(a,b,c){let d,{reference:e,floating:f}=a,g=ab(b),h=$(ab(b)),i=_(h),j=Y(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(Z(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let al=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=ak(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=ak(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function am(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=X(b,a),o=ai(n),p=h[m?"floating"===l?"reference":"floating":l],q=aj(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=aj(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function an(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function ao(a){return P.some(b=>a[b]>=0)}let ap=new Set(["left","top"]);async function aq(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=Y(c),h=Z(c),i="y"===ab(c),j=ap.has(g)?-1:1,k=f&&i?-1:1,l=X(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function ar(){return"undefined"!=typeof window}function as(a){return av(a)?(a.nodeName||"").toLowerCase():"#document"}function at(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function au(a){var b;return null==(b=(av(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function av(a){return!!ar()&&(a instanceof Node||a instanceof at(a).Node)}function aw(a){return!!ar()&&(a instanceof Element||a instanceof at(a).Element)}function ax(a){return!!ar()&&(a instanceof HTMLElement||a instanceof at(a).HTMLElement)}function ay(a){return!!ar()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof at(a).ShadowRoot)}let az=new Set(["inline","contents"]);function aA(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aL(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!az.has(e)}let aB=new Set(["table","td","th"]),aC=[":popover-open",":modal"];function aD(a){return aC.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let aE=["transform","translate","scale","rotate","perspective"],aF=["transform","translate","scale","rotate","perspective","filter"],aG=["paint","layout","strict","content"];function aH(a){let b=aI(),c=aw(a)?aL(a):a;return aE.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||aF.some(a=>(c.willChange||"").includes(a))||aG.some(a=>(c.contain||"").includes(a))}function aI(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aJ=new Set(["html","body","#document"]);function aK(a){return aJ.has(as(a))}function aL(a){return at(a).getComputedStyle(a)}function aM(a){return aw(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aN(a){if("html"===as(a))return a;let b=a.assignedSlot||a.parentNode||ay(a)&&a.host||au(a);return ay(b)?b.host:b}function aO(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aN(b);return aK(c)?b.ownerDocument?b.ownerDocument.body:b.body:ax(c)&&aA(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=at(e);if(f){let a=aP(g);return b.concat(g,g.visualViewport||[],aA(e)?e:[],a&&c?aO(a):[])}return b.concat(e,aO(e,[],c))}function aP(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aQ(a){let b=aL(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=ax(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=S(c)!==f||S(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aR(a){return aw(a)?a:a.contextElement}function aS(a){let b=aR(a);if(!ax(b))return U(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aQ(b),g=(f?S(c.width):c.width)/d,h=(f?S(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let aT=U(0);function aU(a){let b=at(a);return aI()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:aT}function aV(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aR(a),h=U(1);b&&(d?aw(d)&&(h=aS(d)):h=aS(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===at(g))&&e)?aU(g):U(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=at(g),b=d&&aw(d)?at(d):d,c=a,e=aP(c);for(;e&&d&&b!==c;){let a=aS(e),b=e.getBoundingClientRect(),d=aL(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aP(c=at(e))}}return aj({width:l,height:m,x:j,y:k})}function aW(a,b){let c=aM(a).scrollLeft;return b?b.left+c:aV(au(a)).left+c}function aX(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aW(a,d)),y:d.top+b.scrollTop}}let aY=new Set(["absolute","fixed"]);function aZ(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=at(a),d=au(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aI();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=au(a),c=aM(a),d=a.ownerDocument.body,e=R(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=R(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aW(a),h=-c.scrollTop;return"rtl"===aL(d).direction&&(g+=R(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(au(a));else if(aw(b))d=function(a,b){let c=aV(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=ax(a)?aS(a):U(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aU(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return aj(d)}function a$(a){return"static"===aL(a).position}function a_(a,b){if(!ax(a)||"fixed"===aL(a).position)return null;if(b)return b(a);let c=a.offsetParent;return au(a)===c&&(c=c.ownerDocument.body),c}function a0(a,b){var c;let d=at(a);if(aD(a))return d;if(!ax(a)){let b=aN(a);for(;b&&!aK(b);){if(aw(b)&&!a$(b))return b;b=aN(b)}return d}let e=a_(a,b);for(;e&&(c=e,aB.has(as(c)))&&a$(e);)e=a_(e,b);return e&&aK(e)&&a$(e)&&!aH(e)?d:e||function(a){let b=aN(a);for(;ax(b)&&!aK(b);){if(aH(b))return b;if(aD(b))break;b=aN(b)}return null}(a)||d}let a1=async function(a){let b=this.getOffsetParent||a0,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=ax(b),e=au(b),f="fixed"===c,g=aV(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=U(0);if(d||!d&&!f)if(("body"!==as(b)||aA(e))&&(h=aM(b)),d){let a=aV(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aW(e));f&&!d&&e&&(i.x=aW(e));let j=!e||d||f?U(0):aX(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},a2={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=au(d),h=!!b&&aD(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=U(1),k=U(0),l=ax(d);if((l||!l&&!f)&&(("body"!==as(d)||aA(g))&&(i=aM(d)),ax(d))){let a=aV(d);j=aS(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?U(0):aX(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:au,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aD(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aO(a,[],!1).filter(a=>aw(a)&&"body"!==as(a)),e=null,f="fixed"===aL(a).position,g=f?aN(a):a;for(;aw(g)&&!aK(g);){let b=aL(g),c=aH(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&aY.has(e.position)||aA(g)&&!c&&function a(b,c){let d=aN(b);return!(d===c||!aw(d)||aK(d))&&("fixed"===aL(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=aN(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=aZ(b,c,e);return a.top=R(d.top,a.top),a.right=Q(d.right,a.right),a.bottom=Q(d.bottom,a.bottom),a.left=R(d.left,a.left),a},aZ(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:a0,getElementRects:a1,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aQ(a);return{width:b,height:c}},getScale:aS,isElement:aw,isRTL:function(a){return"rtl"===aL(a).direction}};function a3(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let a4=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=X(a,b)||{};if(null==j)return{};let l=ai(k),m={x:c,y:d},n=$(ab(e)),o=_(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=Q(l[q?"top":"left"],w),y=Q(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=R(x,Q(A,z)),C=!i.arrow&&null!=Z(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var a5="undefined"!=typeof document?g.useLayoutEffect:function(){};function a6(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!a6(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!a6(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function a7(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function a8(a,b){let c=a7(a);return Math.round(b*c)/c}function a9(a){let b=g.useRef(a);return a5(()=>{b.current=a}),b}var ba=g.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,l.jsx)(t.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,l.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ba.displayName="Arrow";var bb="Popper",[bc,bd]=m(bb),[be,bf]=bc(bb),bg=a=>{let{__scopePopper:b,children:c}=a,[d,e]=g.useState(null);return(0,l.jsx)(be,{scope:b,anchor:d,onAnchorChange:e,children:c})};bg.displayName=bb;var bh="PopperAnchor",bi=g.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=bf(bh,c),h=g.useRef(null),i=(0,n.s)(b,h);return g.useEffect(()=>{f.onAnchorChange(d?.current||h.current)}),d?null:(0,l.jsx)(t.sG.div,{...e,ref:i})});bi.displayName=bh;var bj="PopperContent",[bk,bl]=bc(bj),bm=g.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:h=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:m=[],collisionPadding:o=0,sticky:p="partial",hideWhenDetached:q=!1,updatePositionStrategy:r="optimized",onPlaced:s,...v}=a,w=bf(bj,c),[x,y]=g.useState(null),z=(0,n.s)(b,a=>y(a)),[A,B]=g.useState(null),C=function(a){let[b,c]=g.useState(void 0);return L(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(A),D=C?.width??0,E=C?.height??0,F="number"==typeof o?o:{top:0,right:0,bottom:0,left:0,...o},G=Array.isArray(m)?m:[m],H=G.length>0,I={padding:F,boundary:G.filter(bq),altBoundary:H},{refs:J,floatingStyles:K,placement:M,isPositioned:N,middlewareData:O}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:f,floating:h}={},transform:j=!0,whileElementsMounted:k,open:l}=a,[m,n]=g.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[o,p]=g.useState(d);a6(o,d)||p(d);let[q,r]=g.useState(null),[s,t]=g.useState(null),u=g.useCallback(a=>{a!==y.current&&(y.current=a,r(a))},[]),v=g.useCallback(a=>{a!==z.current&&(z.current=a,t(a))},[]),w=f||q,x=h||s,y=g.useRef(null),z=g.useRef(null),A=g.useRef(m),B=null!=k,C=a9(k),D=a9(e),E=a9(l),F=g.useCallback(()=>{if(!y.current||!z.current)return;let a={placement:b,strategy:c,middleware:o};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:a2,...c},f={...e.platform,_c:d};return al(a,b,{...e,platform:f})})(y.current,z.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!a6(A.current,b)&&(A.current=b,i.flushSync(()=>{n(b)}))})},[o,b,c,D,E]);a5(()=>{!1===l&&A.current.isPositioned&&(A.current.isPositioned=!1,n(a=>({...a,isPositioned:!1})))},[l]);let G=g.useRef(!1);a5(()=>(G.current=!0,()=>{G.current=!1}),[]),a5(()=>{if(w&&(y.current=w),x&&(z.current=x),w&&x){if(C.current)return C.current(w,x,F);F()}},[w,x,F,C,B]);let H=g.useMemo(()=>({reference:y,floating:z,setReference:u,setFloating:v}),[u,v]),I=g.useMemo(()=>({reference:w,floating:x}),[w,x]),J=g.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=a8(I.floating,m.x),d=a8(I.floating,m.y);return j?{...a,transform:"translate("+b+"px, "+d+"px)",...a7(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,j,I.floating,m.x,m.y]);return g.useMemo(()=>({...m,update:F,refs:H,elements:I,floatingStyles:J}),[m,F,H,I,J])}({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aR(a),l=f||g?[...k?aO(k):[],...aO(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=au(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=T(l),p=T(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-T(e.clientHeight-(l+n))+"px "+-T(k)+"px",threshold:R(0,Q(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||a3(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?aV(a):null;return j&&function b(){let d=aV(a);p&&!a3(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===r}),elements:{reference:w.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await aq(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:e+E,alignmentAxis:h}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=X(a,b),j={x:c,y:d},k=await am(b,i),l=ab(Y(e)),m=$(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=R(c,Q(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=R(c,Q(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=X(a,b),k={x:c,y:d},l=ab(e),m=$(l),n=k[m],o=k[l],p=X(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=ap.has(Y(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...I}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=X(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=Y(h),v=ab(k),w=Y(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[ah(k)]:function(a){let b=ah(a);return[ac(a),b,ac(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=Z(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?ae:ad;return b?ad:ae;case"left":case"right":return b?af:ag;default:return[]}}(Y(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(ac)))),f}(k,s,r,x));let A=[k,...y],B=await am(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=Z(a),e=$(ab(a)),f=_(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=ah(g)),[g,ah(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===ab(b)||D.every(a=>a.overflows[0]>0&&ab(a.placement)===v)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=ab(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...I}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=X(a,b),m=await am(b,l),n=Y(g),o=Z(g),p="y"===ab(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=Q(r-m[e],s),v=Q(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=R(m.left,0),b=R(m.right,0),c=R(m.top,0),d=R(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:R(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:R(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...I,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),A&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?a4({element:c.current,padding:d}).fn(b):{}:c?a4({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:A,padding:j}),br({arrowWidth:D,arrowHeight:E}),q&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=X(a,b);switch(d){case"referenceHidden":{let a=an(await am(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:ao(a)}}}case"escaped":{let a=an(await am(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:ao(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...I})]}),[P,S]=bs(M),U=u(s);L(()=>{N&&U?.()},[N,U]);let V=O.arrow?.x,W=O.arrow?.y,aa=O.arrow?.centerOffset!==0,[ai,aj]=g.useState();return L(()=>{x&&aj(window.getComputedStyle(x).zIndex)},[x]),(0,l.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:N?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ai,"--radix-popper-transform-origin":[O.transformOrigin?.x,O.transformOrigin?.y].join(" "),...O.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,l.jsx)(bk,{scope:c,placedSide:P,onArrowChange:B,arrowX:V,arrowY:W,shouldHideArrow:aa,children:(0,l.jsx)(t.sG.div,{"data-side":P,"data-align":S,...v,ref:z,style:{...v.style,animation:N?void 0:"none"}})})})});bm.displayName=bj;var bn="PopperArrow",bo={top:"bottom",right:"left",bottom:"top",left:"right"},bp=g.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=bl(bn,c),f=bo[e.placedSide];return(0,l.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,l.jsx)(ba,{...d,ref:b,style:{...d.style,display:"block"}})})});function bq(a){return null!==a}bp.displayName=bn;var br=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bs(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bs(a){let[b,c="center"]=a.split("-");return[b,c]}var bt=g.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=g.useState(!1);L(()=>f(!0),[]);let h=c||e&&globalThis?.document?.body;return h?i.createPortal((0,l.jsx)(t.sG.div,{...d,ref:b}),h):null});bt.displayName="Portal";var bu=h[" useInsertionEffect ".trim().toString()]||L;function bv({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,h]=function({defaultProp:a,onChange:b}){let[c,d]=g.useState(a),e=g.useRef(c),f=g.useRef(b);return bu(()=>{f.current=b},[b]),g.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=g.useRef(void 0!==a);g.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,g.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&h.current?.(c)}else f(b)},[i,a,f,h])]}Symbol("RADIX:SYNC_STATE");var bw=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});g.forwardRef((a,b)=>(0,l.jsx)(t.sG.span,{...a,ref:b,style:{...bw,...a.style}})).displayName="VisuallyHidden";var bx=new WeakMap,by=new WeakMap,bz={},bA=0,bB=function(a){return a&&(a.host||bB(a.parentNode))},bC=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bB(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bz[c]||(bz[c]=new WeakMap);var f=bz[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bx.get(a)||0)+1,j=(f.get(a)||0)+1;bx.set(a,i),f.set(a,j),g.push(a),1===i&&e&&by.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bA++,function(){g.forEach(function(a){var b=bx.get(a)-1,e=f.get(a)-1;bx.set(a,b),f.set(a,e),b||(by.has(a)||a.removeAttribute(d),by.delete(a)),e||a.removeAttribute(c)}),--bA||(bx=new WeakMap,bx=new WeakMap,by=new WeakMap,bz={})}},bD=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),bC(d,e,c,"aria-hidden")):function(){return null}},bE=function(){return(bE=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bF(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bG=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),bH="width-before-scroll-bar";function bI(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var bJ="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,bK=new WeakMap;function bL(a){return a}var bM=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=bL),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bE({async:!0,ssr:!1},a),e}(),bN=function(){},bO=g.forwardRef(function(a,b){var c,d,e,f,h=g.useRef(null),i=g.useState({onScrollCapture:bN,onWheelCapture:bN,onTouchMoveCapture:bN}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bF(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return bI(b,a)})},(e=(0,g.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,bJ(function(){var a=bK.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||bI(a,null)}),d.forEach(function(a){b.has(a)||bI(a,e)})}bK.set(f,c)},[c]),f),A=bE(bE({},y),j);return g.createElement(g.Fragment,null,p&&g.createElement(r,{sideCar:bM,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?g.cloneElement(g.Children.only(m),bE(bE({},A),{ref:z})):g.createElement(void 0===w?"div":w,bE({},A,{className:n,ref:z}),m))});bO.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},bO.classNames={fullWidth:bH,zeroRight:bG};var bP=function(a){var b=a.sideCar,c=bF(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return g.createElement(d,bE({},c))};bP.isSideCarExport=!0;var bQ=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},bR=function(){var a=bQ();return function(b,c){g.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},bS=function(){var a=bR();return function(b){return a(b.styles,b.dynamic),null}},bT={left:0,top:0,right:0,gap:0},bU=function(a){return parseInt(a||"",10)||0},bV=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[bU(c),bU(d),bU(e)]},bW=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return bT;var b=bV(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},bX=bS(),bY="data-scroll-locked",bZ=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(bY,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bG," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bH," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bG," .").concat(bG," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(bH," .").concat(bH," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(bY,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},b$=function(){var a=parseInt(document.body.getAttribute(bY)||"0",10);return isFinite(a)?a:0},b_=function(){g.useEffect(function(){return document.body.setAttribute(bY,(b$()+1).toString()),function(){var a=b$()-1;a<=0?document.body.removeAttribute(bY):document.body.setAttribute(bY,a.toString())}},[])},b0=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;b_();var f=g.useMemo(function(){return bW(e)},[e]);return g.createElement(bX,{styles:bZ(f,!b,e,c?"":"!important")})},b1=!1;if("undefined"!=typeof window)try{var b2=Object.defineProperty({},"passive",{get:function(){return b1=!0,!0}});window.addEventListener("test",b2,b2),window.removeEventListener("test",b2,b2)}catch(a){b1=!1}var b3=!!b1&&{passive:!1},b4=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},b5=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),b6(a,d)){var e=b7(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},b6=function(a,b){return"v"===a?b4(b,"overflowY"):b4(b,"overflowX")},b7=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},b8=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=b7(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&b6(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},b9=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},ca=function(a){return[a.deltaX,a.deltaY]},cb=function(a){return a&&"current"in a?a.current:a},cc=0,cd=[];let ce=(d=function(a){var b=g.useRef([]),c=g.useRef([0,0]),d=g.useRef(),e=g.useState(cc++)[0],f=g.useState(bS)[0],h=g.useRef(a);g.useEffect(function(){h.current=a},[a]),g.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(cb),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=g.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=b9(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=b5(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=b5(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return b8(n,b,a,"h"===n?i:j,!0)},[]),j=g.useCallback(function(a){if(cd.length&&cd[cd.length-1]===f){var c="deltaY"in a?ca(a):b9(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(cb).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=g.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=g.useCallback(function(a){c.current=b9(a),d.current=void 0},[]),m=g.useCallback(function(b){k(b.type,ca(b),b.target,i(b,a.lockRef.current))},[]),n=g.useCallback(function(b){k(b.type,b9(b),b.target,i(b,a.lockRef.current))},[]);g.useEffect(function(){return cd.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,b3),document.addEventListener("touchmove",j,b3),document.addEventListener("touchstart",l,b3),function(){cd=cd.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,b3),document.removeEventListener("touchmove",j,b3),document.removeEventListener("touchstart",l,b3)}},[]);var o=a.removeScrollBar,p=a.inert;return g.createElement(g.Fragment,null,p?g.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?g.createElement(b0,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},bM.useMedium(d),bP);var cf=g.forwardRef(function(a,b){return g.createElement(bO,bE({},a,{ref:b,sideCar:ce}))});cf.classNames=bO.classNames;var cg=[" ","Enter","ArrowUp","ArrowDown"],ch=[" ","Enter"],ci="Select",[cj,ck,cl]=function(a){let b=a+"CollectionProvider",[c,d]=m(b),[e,f]=c(b,{collectionRef:{current:null},itemMap:new Map}),h=a=>{let{scope:b,children:c}=a,d=g.useRef(null),f=g.useRef(new Map).current;return(0,l.jsx)(e,{scope:b,itemMap:f,collectionRef:d,children:c})};h.displayName=b;let i=a+"CollectionSlot",j=(0,o.TL)(i),k=g.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=f(i,c),g=(0,n.s)(b,e.collectionRef);return(0,l.jsx)(j,{ref:g,children:d})});k.displayName=i;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,o.TL)(p),s=g.forwardRef((a,b)=>{let{scope:c,children:d,...e}=a,h=g.useRef(null),i=(0,n.s)(b,h),j=f(p,c);return g.useEffect(()=>(j.itemMap.set(h,{ref:h,...e}),()=>void j.itemMap.delete(h))),(0,l.jsx)(r,{...{[q]:""},ref:i,children:d})});return s.displayName=p,[{Provider:h,Slot:k,ItemSlot:s},function(b){let c=f(a+"CollectionConsumer",b);return g.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}(ci),[cm,cn]=m(ci,[cl,bd]),co=bd(),[cp,cq]=cm(ci),[cr,cs]=cm(ci),ct=a=>{let{__scopeSelect:b,children:c,open:d,defaultOpen:e,onOpenChange:f,value:h,defaultValue:i,onValueChange:j,dir:k,name:m,autoComplete:n,disabled:o,required:p,form:q}=a,r=co(b),[t,u]=g.useState(null),[v,w]=g.useState(null),[x,y]=g.useState(!1),z=function(a){let b=g.useContext(s);return a||b||"ltr"}(k),[A,B]=bv({prop:d,defaultProp:e??!1,onChange:f,caller:ci}),[C,D]=bv({prop:h,defaultProp:i,onChange:j,caller:ci}),E=g.useRef(null),F=!t||q||!!t.closest("form"),[G,H]=g.useState(new Set),I=Array.from(G).map(a=>a.props.value).join(";");return(0,l.jsx)(bg,{...r,children:(0,l.jsxs)(cp,{required:p,scope:b,trigger:t,onTriggerChange:u,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:x,onValueNodeHasChildrenChange:y,contentId:O(),value:C,onValueChange:D,open:A,onOpenChange:B,dir:z,triggerPointerDownPosRef:E,disabled:o,children:[(0,l.jsx)(cj.Provider,{scope:b,children:(0,l.jsx)(cr,{scope:a.__scopeSelect,onNativeOptionAdd:g.useCallback(a=>{H(b=>new Set(b).add(a))},[]),onNativeOptionRemove:g.useCallback(a=>{H(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),F?(0,l.jsxs)(c2,{"aria-hidden":!0,required:p,tabIndex:-1,name:m,autoComplete:n,value:C,onChange:a=>D(a.target.value),disabled:o,form:q,children:[void 0===C?(0,l.jsx)("option",{value:""}):null,Array.from(G)]},I):null]})})};ct.displayName=ci;var cu="SelectTrigger",cv=g.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:d=!1,...e}=a,f=co(c),h=cq(cu,c),i=h.disabled||d,j=(0,n.s)(b,h.onTriggerChange),m=ck(c),o=g.useRef("touch"),[p,q,r]=c4(a=>{let b=m().filter(a=>!a.disabled),c=b.find(a=>a.value===h.value),d=c5(b,a,c);void 0!==d&&h.onValueChange(d.value)}),s=a=>{i||(h.onOpenChange(!0),r()),a&&(h.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,l.jsx)(bi,{asChild:!0,...f,children:(0,l.jsx)(t.sG.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:i,"data-disabled":i?"":void 0,"data-placeholder":c3(h.value)?"":void 0,...e,ref:j,onClick:k(e.onClick,a=>{a.currentTarget.focus(),"mouse"!==o.current&&s(a)}),onPointerDown:k(e.onPointerDown,a=>{o.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(s(a),a.preventDefault())}),onKeyDown:k(e.onKeyDown,a=>{let b=""!==p.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||q(a.key),(!b||" "!==a.key)&&cg.includes(a.key)&&(s(),a.preventDefault())})})})});cv.displayName=cu;var cw="SelectValue",cx=g.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,i=cq(cw,c),{onValueNodeHasChildrenChange:j}=i,k=void 0!==f,m=(0,n.s)(b,i.onValueNodeChange);return L(()=>{j(k)},[j,k]),(0,l.jsx)(t.sG.span,{...h,ref:m,style:{pointerEvents:"none"},children:c3(i.value)?(0,l.jsx)(l.Fragment,{children:g}):f})});cx.displayName=cw;var cy=g.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,l.jsx)(t.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});cy.displayName="SelectIcon";var cz=a=>(0,l.jsx)(bt,{asChild:!0,...a});cz.displayName="SelectPortal";var cA="SelectContent",cB=g.forwardRef((a,b)=>{let c=cq(cA,a.__scopeSelect),[d,e]=g.useState();return(L(()=>{e(new DocumentFragment)},[]),c.open)?(0,l.jsx)(cF,{...a,ref:b}):d?i.createPortal((0,l.jsx)(cC,{scope:a.__scopeSelect,children:(0,l.jsx)(cj.Slot,{scope:a.__scopeSelect,children:(0,l.jsx)("div",{children:a.children})})}),d):null});cB.displayName=cA;var[cC,cD]=cm(cA),cE=(0,o.TL)("SelectContent.RemoveScroll"),cF=g.forwardRef((a,b)=>{let{__scopeSelect:c,position:d="item-aligned",onCloseAutoFocus:e,onEscapeKeyDown:f,onPointerDownOutside:h,side:i,sideOffset:j,align:m,alignOffset:o,arrowPadding:p,collisionBoundary:q,collisionPadding:r,sticky:s,hideWhenDetached:t,avoidCollisions:u,...v}=a,w=cq(cA,c),[y,z]=g.useState(null),[C,D]=g.useState(null),E=(0,n.s)(b,a=>z(a)),[G,H]=g.useState(null),[I,J]=g.useState(null),K=ck(c),[L,M]=g.useState(!1),N=g.useRef(!1);g.useEffect(()=>{if(y)return bD(y)},[y]),g.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??B()),document.body.insertAdjacentElement("beforeend",a[1]??B()),A++,()=>{1===A&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),A--}},[]);let O=g.useCallback(a=>{let[b,...c]=K().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&C&&(C.scrollTop=0),c===d&&C&&(C.scrollTop=C.scrollHeight),c?.focus(),document.activeElement!==e))return},[K,C]),P=g.useCallback(()=>O([G,y]),[O,G,y]);g.useEffect(()=>{L&&P()},[L,P]);let{onOpenChange:Q,triggerPointerDownPosRef:R}=w;g.useEffect(()=>{if(y){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(R.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(R.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():y.contains(c.target)||Q(!1),document.removeEventListener("pointermove",b),R.current=null};return null!==R.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[y,Q,R]),g.useEffect(()=>{let a=()=>Q(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[Q]);let[S,T]=c4(a=>{let b=K().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=c5(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),U=g.useCallback((a,b,c)=>{let d=!N.current&&!c;(void 0!==w.value&&w.value===b||d)&&(H(a),d&&(N.current=!0))},[w.value]),V=g.useCallback(()=>y?.focus(),[y]),W=g.useCallback((a,b,c)=>{let d=!N.current&&!c;(void 0!==w.value&&w.value===b||d)&&J(a)},[w.value]),X="popper"===d?cH:cG,Y=X===cH?{side:i,sideOffset:j,align:m,alignOffset:o,arrowPadding:p,collisionBoundary:q,collisionPadding:r,sticky:s,hideWhenDetached:t,avoidCollisions:u}:{};return(0,l.jsx)(cC,{scope:c,content:y,viewport:C,onViewportChange:D,itemRefCallback:U,selectedItem:G,onItemLeave:V,itemTextRefCallback:W,focusSelectedItem:P,selectedItemText:I,position:d,isPositioned:L,searchRef:S,children:(0,l.jsx)(cf,{as:cE,allowPinchZoom:!0,children:(0,l.jsx)(F,{asChild:!0,trapped:w.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:k(e,a=>{w.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,l.jsx)(x,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:(0,l.jsx)(X,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:a=>a.preventDefault(),...v,...Y,onPlaced:()=>M(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:k(v.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||T(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=K().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>O(b)),a.preventDefault()}})})})})})})});cF.displayName="SelectContentImpl";var cG=g.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:d,...e}=a,f=cq(cA,c),h=cD(cA,c),[i,k]=g.useState(null),[m,o]=g.useState(null),p=(0,n.s)(b,a=>o(a)),q=ck(c),r=g.useRef(!1),s=g.useRef(!0),{viewport:u,selectedItem:v,selectedItemText:w,focusSelectedItem:x}=h,y=g.useCallback(()=>{if(f.trigger&&f.valueNode&&i&&m&&u&&v&&w){let a=f.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=f.valueNode.getBoundingClientRect(),e=w.getBoundingClientRect();if("rtl"!==f.dir){let d=e.left-b.left,f=c.left-d,g=a.left-f,h=a.width+g,k=Math.max(h,b.width),l=j(f,[10,Math.max(10,window.innerWidth-10-k)]);i.style.minWidth=h+"px",i.style.left=l+"px"}else{let d=b.right-e.right,f=window.innerWidth-c.right-d,g=window.innerWidth-a.right-f,h=a.width+g,k=Math.max(h,b.width),l=j(f,[10,Math.max(10,window.innerWidth-10-k)]);i.style.minWidth=h+"px",i.style.right=l+"px"}let g=q(),h=window.innerHeight-20,k=u.scrollHeight,l=window.getComputedStyle(m),n=parseInt(l.borderTopWidth,10),o=parseInt(l.paddingTop,10),p=parseInt(l.borderBottomWidth,10),s=n+o+k+parseInt(l.paddingBottom,10)+p,t=Math.min(5*v.offsetHeight,s),x=window.getComputedStyle(u),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=v.offsetHeight/2,C=n+o+(v.offsetTop+B);if(C<=A){let a=g.length>0&&v===g[g.length-1].ref.current;i.style.bottom="0px";let b=Math.max(h-A,B+(a?z:0)+(m.clientHeight-u.offsetTop-u.offsetHeight)+p);i.style.height=C+b+"px"}else{let a=g.length>0&&v===g[0].ref.current;i.style.top="0px";let b=Math.max(A,n+u.offsetTop+(a?y:0)+B);i.style.height=b+(s-C)+"px",u.scrollTop=C-A+u.offsetTop}i.style.margin="10px 0",i.style.minHeight=t+"px",i.style.maxHeight=h+"px",d?.(),requestAnimationFrame(()=>r.current=!0)}},[q,f.trigger,f.valueNode,i,m,u,v,w,f.dir,d]);L(()=>y(),[y]);let[z,A]=g.useState();L(()=>{m&&A(window.getComputedStyle(m).zIndex)},[m]);let B=g.useCallback(a=>{a&&!0===s.current&&(y(),x?.(),s.current=!1)},[y,x]);return(0,l.jsx)(cI,{scope:c,contentWrapper:i,shouldExpandOnScrollRef:r,onScrollButtonChange:B,children:(0,l.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:(0,l.jsx)(t.sG.div,{...e,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...e.style}})})})});cG.displayName="SelectItemAlignedPosition";var cH=g.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=co(c);return(0,l.jsx)(bm,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});cH.displayName="SelectPopperPosition";var[cI,cJ]=cm(cA,{}),cK="SelectViewport",cL=g.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:d,...e}=a,f=cD(cK,c),h=cJ(cK,c),i=(0,n.s)(b,f.onViewportChange),j=g.useRef(0);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:d}),(0,l.jsx)(cj.Slot,{scope:c,children:(0,l.jsx)(t.sG.div,{"data-radix-select-viewport":"",role:"presentation",...e,ref:i,style:{position:"relative",flex:1,overflow:"hidden auto",...e.style},onScroll:k(e.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=h;if(d?.current&&c){let a=Math.abs(j.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}j.current=b.scrollTop})})})]})});cL.displayName=cK;var cM="SelectGroup",[cN,cO]=cm(cM);g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=O();return(0,l.jsx)(cN,{scope:c,id:e,children:(0,l.jsx)(t.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=cM;var cP="SelectLabel";g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=cO(cP,c);return(0,l.jsx)(t.sG.div,{id:e.id,...d,ref:b})}).displayName=cP;var cQ="SelectItem",[cR,cS]=cm(cQ),cT=g.forwardRef((a,b)=>{let{__scopeSelect:c,value:d,disabled:e=!1,textValue:f,...h}=a,i=cq(cQ,c),j=cD(cQ,c),m=i.value===d,[o,p]=g.useState(f??""),[q,r]=g.useState(!1),s=(0,n.s)(b,a=>j.itemRefCallback?.(a,d,e)),u=O(),v=g.useRef("touch"),w=()=>{e||(i.onValueChange(d),i.onOpenChange(!1))};if(""===d)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,l.jsx)(cR,{scope:c,value:d,disabled:e,textId:u,isSelected:m,onItemTextChange:g.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,l.jsx)(cj.ItemSlot,{scope:c,value:d,disabled:e,textValue:o,children:(0,l.jsx)(t.sG.div,{role:"option","aria-labelledby":u,"data-highlighted":q?"":void 0,"aria-selected":m&&q,"data-state":m?"checked":"unchecked","aria-disabled":e||void 0,"data-disabled":e?"":void 0,tabIndex:e?void 0:-1,...h,ref:s,onFocus:k(h.onFocus,()=>r(!0)),onBlur:k(h.onBlur,()=>r(!1)),onClick:k(h.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:k(h.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:k(h.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:k(h.onPointerMove,a=>{v.current=a.pointerType,e?j.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:k(h.onPointerLeave,a=>{a.currentTarget===document.activeElement&&j.onItemLeave?.()}),onKeyDown:k(h.onKeyDown,a=>{(j.searchRef?.current===""||" "!==a.key)&&(ch.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});cT.displayName=cQ;var cU="SelectItemText",cV=g.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,...f}=a,h=cq(cU,c),j=cD(cU,c),k=cS(cU,c),m=cs(cU,c),[o,p]=g.useState(null),q=(0,n.s)(b,a=>p(a),k.onItemTextChange,a=>j.itemTextRefCallback?.(a,k.value,k.disabled)),r=o?.textContent,s=g.useMemo(()=>(0,l.jsx)("option",{value:k.value,disabled:k.disabled,children:r},k.value),[k.disabled,k.value,r]),{onNativeOptionAdd:u,onNativeOptionRemove:v}=m;return L(()=>(u(s),()=>v(s)),[u,v,s]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.sG.span,{id:k.textId,...f,ref:q}),k.isSelected&&h.valueNode&&!h.valueNodeHasChildren?i.createPortal(f.children,h.valueNode):null]})});cV.displayName=cU;var cW="SelectItemIndicator",cX=g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return cS(cW,c).isSelected?(0,l.jsx)(t.sG.span,{"aria-hidden":!0,...d,ref:b}):null});cX.displayName=cW;var cY="SelectScrollUpButton",cZ=g.forwardRef((a,b)=>{let c=cD(cY,a.__scopeSelect),d=cJ(cY,a.__scopeSelect),[e,f]=g.useState(!1),h=(0,n.s)(b,d.onScrollButtonChange);return L(()=>{if(c.viewport&&c.isPositioned){let a=function(){f(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,l.jsx)(c0,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});cZ.displayName=cY;var c$="SelectScrollDownButton",c_=g.forwardRef((a,b)=>{let c=cD(c$,a.__scopeSelect),d=cJ(c$,a.__scopeSelect),[e,f]=g.useState(!1),h=(0,n.s)(b,d.onScrollButtonChange);return L(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;f(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,l.jsx)(c0,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});c_.displayName=c$;var c0=g.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:d,...e}=a,f=cD("SelectScrollButton",c),h=g.useRef(null),i=ck(c),j=g.useCallback(()=>{null!==h.current&&(window.clearInterval(h.current),h.current=null)},[]);return g.useEffect(()=>()=>j(),[j]),L(()=>{let a=i().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[i]),(0,l.jsx)(t.sG.div,{"aria-hidden":!0,...e,ref:b,style:{flexShrink:0,...e.style},onPointerDown:k(e.onPointerDown,()=>{null===h.current&&(h.current=window.setInterval(d,50))}),onPointerMove:k(e.onPointerMove,()=>{f.onItemLeave?.(),null===h.current&&(h.current=window.setInterval(d,50))}),onPointerLeave:k(e.onPointerLeave,()=>{j()})})});g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,l.jsx)(t.sG.div,{"aria-hidden":!0,...d,ref:b})}).displayName="SelectSeparator";var c1="SelectArrow";g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=co(c),f=cq(c1,c),g=cD(c1,c);return f.open&&"popper"===g.position?(0,l.jsx)(bp,{...e,...d,ref:b}):null}).displayName=c1;var c2=g.forwardRef(({__scopeSelect:a,value:b,...c},d)=>{let e=g.useRef(null),f=(0,n.s)(d,e),h=function(a){let b=g.useRef({value:a,previous:a});return g.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(b);return g.useEffect(()=>{let a=e.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,l.jsx)(t.sG.select,{...c,style:{...bw,...c.style},ref:f,defaultValue:b})});function c3(a){return""===a||void 0===a}function c4(a){let b=u(a),c=g.useRef(""),d=g.useRef(0),e=g.useCallback(a=>{let e=c.current+a;b(e),function a(b){c.current=b,window.clearTimeout(d.current),""!==b&&(d.current=window.setTimeout(()=>a(""),1e3))}(e)},[b]),f=g.useCallback(()=>{c.current="",window.clearTimeout(d.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(d.current),[]),[c,e,f]}function c5(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}c2.displayName="SelectBubbleInput";var c6=ct,c7=cv,c8=cx,c9=cy,da=cz,db=cB,dc=cL,dd=cT,de=cV,df=cX,dg=cZ,dh=c_},26499:(a,b,c)=>{c.d(b,{JM:()=>i,Kd:()=>h,Wk:()=>j,a$:()=>g});var d=c(38291),e=c(84324);let f=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),a.message=JSON.stringify(b,e.k8,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},g=(0,d.xI)("$ZodError",f),h=(0,d.xI)("$ZodError",f,{Parent:Error});function i(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}}function j(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d}},27605:(a,b,c)=>{c.d(b,{Gb:()=>E,Jt:()=>p,Op:()=>w,hZ:()=>q,lN:()=>z,mN:()=>ah,xI:()=>D,xW:()=>v});var d=c(43210),e=a=>a instanceof Date,f=a=>null==a,g=a=>!f(a)&&!Array.isArray(a)&&"object"==typeof a&&!e(a),h=a=>g(a)&&a.target?"checkbox"===a.target.type?a.target.checked:a.target.value:a,i=(a,b)=>a.has((a=>a.substring(0,a.search(/\.\d+(\.|$)/))||a)(b)),j="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function k(a){let b,c=Array.isArray(a),d="undefined"!=typeof FileList&&a instanceof FileList;if(a instanceof Date)b=new Date(a);else if(!(!(j&&(a instanceof Blob||d))&&(c||g(a))))return a;else if(b=c?[]:{},c||(a=>{let b=a.constructor&&a.constructor.prototype;return g(b)&&b.hasOwnProperty("isPrototypeOf")})(a))for(let c in a)a.hasOwnProperty(c)&&(b[c]=k(a[c]));else b=a;return b}var l=a=>/^\w*$/.test(a),m=a=>void 0===a,n=a=>Array.isArray(a)?a.filter(Boolean):[],o=a=>n(a.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(a,b,c)=>{if(!b||!g(a))return c;let d=(l(b)?[b]:o(b)).reduce((a,b)=>f(a)?a:a[b],a);return m(d)||d===a?m(a[b])?c:a[b]:d},q=(a,b,c)=>{let d=-1,e=l(b)?[b]:o(b),f=e.length,h=f-1;for(;++d<f;){let b=e[d],f=c;if(d!==h){let c=a[b];f=g(c)||Array.isArray(c)?c:isNaN(+e[d+1])?{}:[]}if("__proto__"===b||"constructor"===b||"prototype"===b)return;a[b]=f,a=a[b]}};let r={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},s={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},t={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},u=d.createContext(null);u.displayName="HookFormContext";let v=()=>d.useContext(u),w=a=>{let{children:b,...c}=a;return d.createElement(u.Provider,{value:c},b)};var x=(a,b,c,d=!0)=>{let e={defaultValues:b._defaultValues};for(let f in a)Object.defineProperty(e,f,{get:()=>(b._proxyFormState[f]!==s.all&&(b._proxyFormState[f]=!d||s.all),c&&(c[f]=!0),a[f])});return e};let y="undefined"!=typeof window?d.useLayoutEffect:d.useEffect;function z(a){let b=v(),{control:c=b.control,disabled:e,name:f,exact:g}=a||{},[h,i]=d.useState(c._formState),j=d.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return y(()=>c._subscribe({name:f,formState:j.current,exact:g,callback:a=>{e||i({...c._formState,...a})}}),[f,e,g]),d.useEffect(()=>{j.current.isValid&&c._setValid(!0)},[c]),d.useMemo(()=>x(h,c,j.current,!1),[h,c])}var A=(a,b,c,d,e)=>"string"==typeof a?(d&&b.watch.add(a),p(c,a,e)):Array.isArray(a)?a.map(a=>(d&&b.watch.add(a),p(c,a))):(d&&(b.watchAll=!0),c),B=a=>f(a)||"object"!=typeof a;function C(a,b,c=new WeakSet){if(B(a)||B(b))return a===b;if(e(a)&&e(b))return a.getTime()===b.getTime();let d=Object.keys(a),f=Object.keys(b);if(d.length!==f.length)return!1;if(c.has(a)||c.has(b))return!0;for(let h of(c.add(a),c.add(b),d)){let d=a[h];if(!f.includes(h))return!1;if("ref"!==h){let a=b[h];if(e(d)&&e(a)||g(d)&&g(a)||Array.isArray(d)&&Array.isArray(a)?!C(d,a,c):d!==a)return!1}}return!0}let D=a=>a.render(function(a){let b=v(),{name:c,disabled:e,control:f=b.control,shouldUnregister:g,defaultValue:j}=a,l=i(f._names.array,c),n=d.useMemo(()=>p(f._formValues,c,p(f._defaultValues,c,j)),[f,c,j]),o=function(a){let b=v(),{control:c=b.control,name:e,defaultValue:f,disabled:g,exact:h,compute:i}=a||{},j=d.useRef(f),k=d.useRef(i),l=d.useRef(void 0);k.current=i;let m=d.useMemo(()=>c._getWatch(e,j.current),[c,e]),[n,o]=d.useState(k.current?k.current(m):m);return y(()=>c._subscribe({name:e,formState:{values:!0},exact:h,callback:a=>{if(!g){let b=A(e,c._names,a.values||c._formValues,!1,j.current);if(k.current){let a=k.current(b);C(a,l.current)||(o(a),l.current=a)}else o(b)}}}),[c,g,e,h]),d.useEffect(()=>c._removeUnmounted()),n}({control:f,name:c,defaultValue:n,exact:!0}),s=z({control:f,name:c,exact:!0}),t=d.useRef(a),u=d.useRef(f.register(c,{...a.rules,value:o,..."boolean"==typeof a.disabled?{disabled:a.disabled}:{}}));t.current=a;let w=d.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!p(s.errors,c)},isDirty:{enumerable:!0,get:()=>!!p(s.dirtyFields,c)},isTouched:{enumerable:!0,get:()=>!!p(s.touchedFields,c)},isValidating:{enumerable:!0,get:()=>!!p(s.validatingFields,c)},error:{enumerable:!0,get:()=>p(s.errors,c)}}),[s,c]),x=d.useCallback(a=>u.current.onChange({target:{value:h(a),name:c},type:r.CHANGE}),[c]),B=d.useCallback(()=>u.current.onBlur({target:{value:p(f._formValues,c),name:c},type:r.BLUR}),[c,f._formValues]),D=d.useCallback(a=>{let b=p(f._fields,c);b&&a&&(b._f.ref={focus:()=>a.focus&&a.focus(),select:()=>a.select&&a.select(),setCustomValidity:b=>a.setCustomValidity(b),reportValidity:()=>a.reportValidity()})},[f._fields,c]),E=d.useMemo(()=>({name:c,value:o,..."boolean"==typeof e||s.disabled?{disabled:s.disabled||e}:{},onChange:x,onBlur:B,ref:D}),[c,e,s.disabled,x,B,D,o]);return d.useEffect(()=>{let a=f._options.shouldUnregister||g;f.register(c,{...t.current.rules,..."boolean"==typeof t.current.disabled?{disabled:t.current.disabled}:{}});let b=(a,b)=>{let c=p(f._fields,a);c&&c._f&&(c._f.mount=b)};if(b(c,!0),a){let a=k(p(f._options.defaultValues,c));q(f._defaultValues,c,a),m(p(f._formValues,c))&&q(f._formValues,c,a)}return l||f.register(c),()=>{(l?a&&!f._state.action:a)?f.unregister(c):b(c,!1)}},[c,f,l,g]),d.useEffect(()=>{f._setDisabledField({disabled:e,name:c})},[e,c,f]),d.useMemo(()=>({field:E,formState:s,fieldState:w}),[E,s,w])}(a));var E=(a,b,c,d,e)=>b?{...c[a],types:{...c[a]&&c[a].types?c[a].types:{},[d]:e||!0}}:{},F=a=>Array.isArray(a)?a:[a],G=()=>{let a=[];return{get observers(){return a},next:b=>{for(let c of a)c.next&&c.next(b)},subscribe:b=>(a.push(b),{unsubscribe:()=>{a=a.filter(a=>a!==b)}}),unsubscribe:()=>{a=[]}}},H=a=>g(a)&&!Object.keys(a).length,I=a=>"function"==typeof a,J=a=>{if(!j)return!1;let b=a?a.ownerDocument:0;return a instanceof(b&&b.defaultView?b.defaultView.HTMLElement:HTMLElement)},K=a=>J(a)&&a.isConnected;function L(a,b){let c=Array.isArray(b)?b:l(b)?[b]:o(b),d=1===c.length?a:function(a,b){let c=b.slice(0,-1).length,d=0;for(;d<c;)a=m(a)?d++:a[b[d++]];return a}(a,c),e=c.length-1,f=c[e];return d&&delete d[f],0!==e&&(g(d)&&H(d)||Array.isArray(d)&&function(a){for(let b in a)if(a.hasOwnProperty(b)&&!m(a[b]))return!1;return!0}(d))&&L(a,c.slice(0,-1)),a}var M=a=>{for(let b in a)if(I(a[b]))return!0;return!1};function N(a,b={}){let c=Array.isArray(a);if(g(a)||c)for(let c in a)Array.isArray(a[c])||g(a[c])&&!M(a[c])?(b[c]=Array.isArray(a[c])?[]:{},N(a[c],b[c])):f(a[c])||(b[c]=!0);return b}var O=(a,b)=>(function a(b,c,d){let e=Array.isArray(b);if(g(b)||e)for(let e in b)Array.isArray(b[e])||g(b[e])&&!M(b[e])?m(c)||B(d[e])?d[e]=Array.isArray(b[e])?N(b[e],[]):{...N(b[e])}:a(b[e],f(c)?{}:c[e],d[e]):d[e]=!C(b[e],c[e]);return d})(a,b,N(b));let P={value:!1,isValid:!1},Q={value:!0,isValid:!0};var R=a=>{if(Array.isArray(a)){if(a.length>1){let b=a.filter(a=>a&&a.checked&&!a.disabled).map(a=>a.value);return{value:b,isValid:!!b.length}}return a[0].checked&&!a[0].disabled?a[0].attributes&&!m(a[0].attributes.value)?m(a[0].value)||""===a[0].value?Q:{value:a[0].value,isValid:!0}:Q:P}return P},S=(a,{valueAsNumber:b,valueAsDate:c,setValueAs:d})=>m(a)?a:b?""===a?NaN:a?+a:a:c&&"string"==typeof a?new Date(a):d?d(a):a;let T={isValid:!1,value:null};var U=a=>Array.isArray(a)?a.reduce((a,b)=>b&&b.checked&&!b.disabled?{isValid:!0,value:b.value}:a,T):T;function V(a){let b=a.ref;return"file"===b.type?b.files:"radio"===b.type?U(a.refs).value:"select-multiple"===b.type?[...b.selectedOptions].map(({value:a})=>a):"checkbox"===b.type?R(a.refs).value:S(m(b.value)?a.ref.value:b.value,a)}var W=a=>m(a)?a:a instanceof RegExp?a.source:g(a)?a.value instanceof RegExp?a.value.source:a.value:a,X=a=>({isOnSubmit:!a||a===s.onSubmit,isOnBlur:a===s.onBlur,isOnChange:a===s.onChange,isOnAll:a===s.all,isOnTouch:a===s.onTouched});let Y="AsyncFunction";var Z=a=>!!a&&!!a.validate&&!!(I(a.validate)&&a.validate.constructor.name===Y||g(a.validate)&&Object.values(a.validate).find(a=>a.constructor.name===Y)),$=(a,b,c)=>!c&&(b.watchAll||b.watch.has(a)||[...b.watch].some(b=>a.startsWith(b)&&/^\.\w+/.test(a.slice(b.length))));let _=(a,b,c,d)=>{for(let e of c||Object.keys(a)){let c=p(a,e);if(c){let{_f:a,...f}=c;if(a){if(a.refs&&a.refs[0]&&b(a.refs[0],e)&&!d)return!0;else if(a.ref&&b(a.ref,a.name)&&!d)return!0;else if(_(f,b))break}else if(g(f)&&_(f,b))break}}};function aa(a,b,c){let d=p(a,c);if(d||l(c))return{error:d,name:c};let e=c.split(".");for(;e.length;){let d=e.join("."),f=p(b,d),g=p(a,d);if(f&&!Array.isArray(f)&&c!==d)break;if(g&&g.type)return{name:d,error:g};if(g&&g.root&&g.root.type)return{name:`${d}.root`,error:g.root};e.pop()}return{name:c}}var ab=(a,b,c)=>{let d=F(p(a,c));return q(d,"root",b[c]),q(a,c,d),a},ac=a=>"string"==typeof a;function ad(a,b,c="validate"){if(ac(a)||Array.isArray(a)&&a.every(ac)||"boolean"==typeof a&&!a)return{type:c,message:ac(a)?a:"",ref:b}}var ae=a=>!g(a)||a instanceof RegExp?{value:a,message:""}:a,af=async(a,b,c,d,e,h)=>{let{ref:i,refs:j,required:k,maxLength:l,minLength:n,min:o,max:q,pattern:r,validate:s,name:u,valueAsNumber:v,mount:w}=a._f,x=p(c,u);if(!w||b.has(u))return{};let y=j?j[0]:i,z=a=>{e&&y.reportValidity&&(y.setCustomValidity("boolean"==typeof a?"":a||""),y.reportValidity())},A={},B="radio"===i.type,C="checkbox"===i.type,D=(v||"file"===i.type)&&m(i.value)&&m(x)||J(i)&&""===i.value||""===x||Array.isArray(x)&&!x.length,F=E.bind(null,u,d,A),G=(a,b,c,d=t.maxLength,e=t.minLength)=>{let f=a?b:c;A[u]={type:a?d:e,message:f,ref:i,...F(a?d:e,f)}};if(h?!Array.isArray(x)||!x.length:k&&(!(B||C)&&(D||f(x))||"boolean"==typeof x&&!x||C&&!R(j).isValid||B&&!U(j).isValid)){let{value:a,message:b}=ac(k)?{value:!!k,message:k}:ae(k);if(a&&(A[u]={type:t.required,message:b,ref:y,...F(t.required,b)},!d))return z(b),A}if(!D&&(!f(o)||!f(q))){let a,b,c=ae(q),e=ae(o);if(f(x)||isNaN(x)){let d=i.valueAsDate||new Date(x),f=a=>new Date(new Date().toDateString()+" "+a),g="time"==i.type,h="week"==i.type;"string"==typeof c.value&&x&&(a=g?f(x)>f(c.value):h?x>c.value:d>new Date(c.value)),"string"==typeof e.value&&x&&(b=g?f(x)<f(e.value):h?x<e.value:d<new Date(e.value))}else{let d=i.valueAsNumber||(x?+x:x);f(c.value)||(a=d>c.value),f(e.value)||(b=d<e.value)}if((a||b)&&(G(!!a,c.message,e.message,t.max,t.min),!d))return z(A[u].message),A}if((l||n)&&!D&&("string"==typeof x||h&&Array.isArray(x))){let a=ae(l),b=ae(n),c=!f(a.value)&&x.length>+a.value,e=!f(b.value)&&x.length<+b.value;if((c||e)&&(G(c,a.message,b.message),!d))return z(A[u].message),A}if(r&&!D&&"string"==typeof x){let{value:a,message:b}=ae(r);if(a instanceof RegExp&&!x.match(a)&&(A[u]={type:t.pattern,message:b,ref:i,...F(t.pattern,b)},!d))return z(b),A}if(s){if(I(s)){let a=ad(await s(x,c),y);if(a&&(A[u]={...a,...F(t.validate,a.message)},!d))return z(a.message),A}else if(g(s)){let a={};for(let b in s){if(!H(a)&&!d)break;let e=ad(await s[b](x,c),y,b);e&&(a={...e,...F(b,e.message)},z(e.message),d&&(A[u]=a))}if(!H(a)&&(A[u]={ref:y,...a},!d))return A}}return z(!0),A};let ag={mode:s.onSubmit,reValidateMode:s.onChange,shouldFocusError:!0};function ah(a={}){let b=d.useRef(void 0),c=d.useRef(void 0),[l,o]=d.useState({isDirty:!1,isValidating:!1,isLoading:I(a.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1,isReady:!1,defaultValues:I(a.defaultValues)?void 0:a.defaultValues});if(!b.current)if(a.formControl)b.current={...a.formControl,formState:l},a.defaultValues&&!I(a.defaultValues)&&a.formControl.reset(a.defaultValues,a.resetOptions);else{let{formControl:c,...d}=function(a={}){let b,c={...ag,...a},d={submitCount:0,isDirty:!1,isReady:!1,isLoading:I(c.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:c.errors||{},disabled:c.disabled||!1},l={},o=(g(c.defaultValues)||g(c.values))&&k(c.defaultValues||c.values)||{},t=c.shouldUnregister?{}:k(o),u={action:!1,mount:!1,watch:!1},v={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},w=0,x={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},y={...x},z={array:G(),state:G()},B=c.criteriaMode===s.all,D=async a=>{if(!c.disabled&&(x.isValid||y.isValid||a)){let a=c.resolver?H((await P()).errors):await R(l,!0);a!==d.isValid&&z.state.next({isValid:a})}},E=(a,b)=>{!c.disabled&&(x.isValidating||x.validatingFields||y.isValidating||y.validatingFields)&&((a||Array.from(v.mount)).forEach(a=>{a&&(b?q(d.validatingFields,a,b):L(d.validatingFields,a))}),z.state.next({validatingFields:d.validatingFields,isValidating:!H(d.validatingFields)}))},M=(a,b,c,d)=>{let e=p(l,a);if(e){let f=p(t,a,m(c)?p(o,a):c);m(f)||d&&d.defaultChecked||b?q(t,a,b?f:V(e._f)):Y(a,f),u.mount&&D()}},N=(a,b,e,f,g)=>{let h=!1,i=!1,j={name:a};if(!c.disabled){if(!e||f){(x.isDirty||y.isDirty)&&(i=d.isDirty,d.isDirty=j.isDirty=T(),h=i!==j.isDirty);let c=C(p(o,a),b);i=!!p(d.dirtyFields,a),c?L(d.dirtyFields,a):q(d.dirtyFields,a,!0),j.dirtyFields=d.dirtyFields,h=h||(x.dirtyFields||y.dirtyFields)&&!c!==i}if(e){let b=p(d.touchedFields,a);b||(q(d.touchedFields,a,e),j.touchedFields=d.touchedFields,h=h||(x.touchedFields||y.touchedFields)&&b!==e)}h&&g&&z.state.next(j)}return h?j:{}},P=async a=>{E(a,!0);let b=await c.resolver(t,c.context,((a,b,c,d)=>{let e={};for(let c of a){let a=p(b,c);a&&q(e,c,a._f)}return{criteriaMode:c,names:[...a],fields:e,shouldUseNativeValidation:d}})(a||v.mount,l,c.criteriaMode,c.shouldUseNativeValidation));return E(a),b},Q=async a=>{let{errors:b}=await P(a);if(a)for(let c of a){let a=p(b,c);a?q(d.errors,c,a):L(d.errors,c)}else d.errors=b;return b},R=async(a,b,e={valid:!0})=>{for(let f in a){let g=a[f];if(g){let{_f:a,...h}=g;if(a){let h=v.array.has(a.name),i=g._f&&Z(g._f);i&&x.validatingFields&&E([f],!0);let j=await af(g,v.disabled,t,B,c.shouldUseNativeValidation&&!b,h);if(i&&x.validatingFields&&E([f]),j[a.name]&&(e.valid=!1,b))break;b||(p(j,a.name)?h?ab(d.errors,j,a.name):q(d.errors,a.name,j[a.name]):L(d.errors,a.name))}H(h)||await R(h,b,e)}}return e.valid},T=(a,b)=>!c.disabled&&(a&&b&&q(t,a,b),!C(aj(),o)),U=(a,b,c)=>A(a,v,{...u.mount?t:m(b)?o:"string"==typeof a?{[a]:b}:b},c,b),Y=(a,b,c={})=>{let d=p(l,a),e=b;if(d){let c=d._f;c&&(c.disabled||q(t,a,S(b,c)),e=J(c.ref)&&f(b)?"":b,"select-multiple"===c.ref.type?[...c.ref.options].forEach(a=>a.selected=e.includes(a.value)):c.refs?"checkbox"===c.ref.type?c.refs.forEach(a=>{a.defaultChecked&&a.disabled||(Array.isArray(e)?a.checked=!!e.find(b=>b===a.value):a.checked=e===a.value||!!e)}):c.refs.forEach(a=>a.checked=a.value===e):"file"===c.ref.type?c.ref.value="":(c.ref.value=e,c.ref.type||z.state.next({name:a,values:k(t)})))}(c.shouldDirty||c.shouldTouch)&&N(a,e,c.shouldTouch,c.shouldDirty,!0),c.shouldValidate&&ai(a)},ac=(a,b,c)=>{for(let d in b){if(!b.hasOwnProperty(d))return;let f=b[d],h=a+"."+d,i=p(l,h);(v.array.has(a)||g(f)||i&&!i._f)&&!e(f)?ac(h,f,c):Y(h,f,c)}},ad=(a,b,c={})=>{let e=p(l,a),g=v.array.has(a),h=k(b);q(t,a,h),g?(z.array.next({name:a,values:k(t)}),(x.isDirty||x.dirtyFields||y.isDirty||y.dirtyFields)&&c.shouldDirty&&z.state.next({name:a,dirtyFields:O(o,t),isDirty:T(a,h)})):!e||e._f||f(h)?Y(a,h,c):ac(a,h,c),$(a,v)&&z.state.next({...d,name:a}),z.state.next({name:u.mount?a:void 0,values:k(t)})},ae=async a=>{u.mount=!0;let f=a.target,g=f.name,i=!0,j=p(l,g),m=a=>{i=Number.isNaN(a)||e(a)&&isNaN(a.getTime())||C(a,p(t,g,a))},n=X(c.mode),o=X(c.reValidateMode);if(j){let e,u,O,Q=f.type?V(j._f):h(a),S=a.type===r.BLUR||a.type===r.FOCUS_OUT,T=!((O=j._f).mount&&(O.required||O.min||O.max||O.maxLength||O.minLength||O.pattern||O.validate))&&!c.resolver&&!p(d.errors,g)&&!j._f.deps||(s=S,A=p(d.touchedFields,g),F=d.isSubmitted,G=o,!(I=n).isOnAll&&(!F&&I.isOnTouch?!(A||s):(F?G.isOnBlur:I.isOnBlur)?!s:(F?!G.isOnChange:!I.isOnChange)||s)),U=$(g,v,S);q(t,g,Q),S?(j._f.onBlur&&j._f.onBlur(a),b&&b(0)):j._f.onChange&&j._f.onChange(a);let W=N(g,Q,S),X=!H(W)||U;if(S||z.state.next({name:g,type:a.type,values:k(t)}),T)return(x.isValid||y.isValid)&&("onBlur"===c.mode?S&&D():S||D()),X&&z.state.next({name:g,...U?{}:W});if(!S&&U&&z.state.next({...d}),c.resolver){let{errors:a}=await P([g]);if(m(Q),i){let b=aa(d.errors,l,g),c=aa(a,l,b.name||g);e=c.error,g=c.name,u=H(a)}}else E([g],!0),e=(await af(j,v.disabled,t,B,c.shouldUseNativeValidation))[g],E([g]),m(Q),i&&(e?u=!1:(x.isValid||y.isValid)&&(u=await R(l,!0)));if(i){j._f.deps&&ai(j._f.deps);var s,A,F,G,I,J=g,K=u,M=e;let a=p(d.errors,J),f=(x.isValid||y.isValid)&&"boolean"==typeof K&&d.isValid!==K;if(c.delayError&&M){let a;a=()=>{q(d.errors,J,M),z.state.next({errors:d.errors})},(b=b=>{clearTimeout(w),w=setTimeout(a,b)})(c.delayError)}else clearTimeout(w),b=null,M?q(d.errors,J,M):L(d.errors,J);if((M?!C(a,M):a)||!H(W)||f){let a={...W,...f&&"boolean"==typeof K?{isValid:K}:{},errors:d.errors,name:J};d={...d,...a},z.state.next(a)}}}},ah=(a,b)=>{if(p(d.errors,b)&&a.focus)return a.focus(),1},ai=async(a,b={})=>{let e,f,g=F(a);if(c.resolver){let b=await Q(m(a)?a:g);e=H(b),f=a?!g.some(a=>p(b,a)):e}else a?((f=(await Promise.all(g.map(async a=>{let b=p(l,a);return await R(b&&b._f?{[a]:b}:b)}))).every(Boolean))||d.isValid)&&D():f=e=await R(l);return z.state.next({..."string"!=typeof a||(x.isValid||y.isValid)&&e!==d.isValid?{}:{name:a},...c.resolver||!a?{isValid:e}:{},errors:d.errors}),b.shouldFocus&&!f&&_(l,ah,a?g:v.mount),f},aj=a=>{let b={...u.mount?t:o};return m(a)?b:"string"==typeof a?p(b,a):a.map(a=>p(b,a))},ak=(a,b)=>({invalid:!!p((b||d).errors,a),isDirty:!!p((b||d).dirtyFields,a),error:p((b||d).errors,a),isValidating:!!p(d.validatingFields,a),isTouched:!!p((b||d).touchedFields,a)}),al=(a,b,c)=>{let e=(p(l,a,{_f:{}})._f||{}).ref,{ref:f,message:g,type:h,...i}=p(d.errors,a)||{};q(d.errors,a,{...i,...b,ref:e}),z.state.next({name:a,errors:d.errors,isValid:!1}),c&&c.shouldFocus&&e&&e.focus&&e.focus()},am=a=>z.state.subscribe({next:b=>{let c,e,f;c=a.name,e=b.name,f=a.exact,(!c||!e||c===e||F(c).some(a=>a&&(f?a===e:a.startsWith(e)||e.startsWith(a))))&&((a,b,c,d)=>{c(a);let{name:e,...f}=a;return H(f)||Object.keys(f).length>=Object.keys(b).length||Object.keys(f).find(a=>b[a]===(!d||s.all))})(b,a.formState||x,au,a.reRenderRoot)&&a.callback({values:{...t},...d,...b,defaultValues:o})}}).unsubscribe,an=(a,b={})=>{for(let e of a?F(a):v.mount)v.mount.delete(e),v.array.delete(e),b.keepValue||(L(l,e),L(t,e)),b.keepError||L(d.errors,e),b.keepDirty||L(d.dirtyFields,e),b.keepTouched||L(d.touchedFields,e),b.keepIsValidating||L(d.validatingFields,e),c.shouldUnregister||b.keepDefaultValue||L(o,e);z.state.next({values:k(t)}),z.state.next({...d,...!b.keepDirty?{}:{isDirty:T()}}),b.keepIsValid||D()},ao=({disabled:a,name:b})=>{("boolean"==typeof a&&u.mount||a||v.disabled.has(b))&&(a?v.disabled.add(b):v.disabled.delete(b))},ap=(a,b={})=>{let d=p(l,a),e="boolean"==typeof b.disabled||"boolean"==typeof c.disabled;return(q(l,a,{...d||{},_f:{...d&&d._f?d._f:{ref:{name:a}},name:a,mount:!0,...b}}),v.mount.add(a),d)?ao({disabled:"boolean"==typeof b.disabled?b.disabled:c.disabled,name:a}):M(a,!0,b.value),{...e?{disabled:b.disabled||c.disabled}:{},...c.progressive?{required:!!b.required,min:W(b.min),max:W(b.max),minLength:W(b.minLength),maxLength:W(b.maxLength),pattern:W(b.pattern)}:{},name:a,onChange:ae,onBlur:ae,ref:e=>{if(e){let c;ap(a,b),d=p(l,a);let f=m(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,g="radio"===(c=f).type||"checkbox"===c.type,h=d._f.refs||[];(g?h.find(a=>a===f):f===d._f.ref)||(q(l,a,{_f:{...d._f,...g?{refs:[...h.filter(K),f,...Array.isArray(p(o,a))?[{}]:[]],ref:{type:f.type,name:a}}:{ref:f}}}),M(a,!1,void 0,f))}else(d=p(l,a,{}))._f&&(d._f.mount=!1),(c.shouldUnregister||b.shouldUnregister)&&!(i(v.array,a)&&u.action)&&v.unMount.add(a)}}},aq=()=>c.shouldFocusError&&_(l,ah,v.mount),ar=(a,b)=>async e=>{let f;e&&(e.preventDefault&&e.preventDefault(),e.persist&&e.persist());let g=k(t);if(z.state.next({isSubmitting:!0}),c.resolver){let{errors:a,values:b}=await P();d.errors=a,g=k(b)}else await R(l);if(v.disabled.size)for(let a of v.disabled)L(g,a);if(L(d.errors,"root"),H(d.errors)){z.state.next({errors:{}});try{await a(g,e)}catch(a){f=a}}else b&&await b({...d.errors},e),aq(),setTimeout(aq);if(z.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:H(d.errors)&&!f,submitCount:d.submitCount+1,errors:d.errors}),f)throw f},as=(a,b={})=>{let e=a?k(a):o,f=k(e),g=H(a),h=g?o:f;if(b.keepDefaultValues||(o=e),!b.keepValues){if(b.keepDirtyValues)for(let a of Array.from(new Set([...v.mount,...Object.keys(O(o,t))])))p(d.dirtyFields,a)?q(h,a,p(t,a)):ad(a,p(h,a));else{if(j&&m(a))for(let a of v.mount){let b=p(l,a);if(b&&b._f){let a=Array.isArray(b._f.refs)?b._f.refs[0]:b._f.ref;if(J(a)){let b=a.closest("form");if(b){b.reset();break}}}}if(b.keepFieldsRef)for(let a of v.mount)ad(a,p(h,a));else l={}}t=c.shouldUnregister?b.keepDefaultValues?k(o):{}:k(h),z.array.next({values:{...h}}),z.state.next({values:{...h}})}v={mount:b.keepDirtyValues?v.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},u.mount=!x.isValid||!!b.keepIsValid||!!b.keepDirtyValues,u.watch=!!c.shouldUnregister,z.state.next({submitCount:b.keepSubmitCount?d.submitCount:0,isDirty:!g&&(b.keepDirty?d.isDirty:!!(b.keepDefaultValues&&!C(a,o))),isSubmitted:!!b.keepIsSubmitted&&d.isSubmitted,dirtyFields:g?{}:b.keepDirtyValues?b.keepDefaultValues&&t?O(o,t):d.dirtyFields:b.keepDefaultValues&&a?O(o,a):b.keepDirty?d.dirtyFields:{},touchedFields:b.keepTouched?d.touchedFields:{},errors:b.keepErrors?d.errors:{},isSubmitSuccessful:!!b.keepIsSubmitSuccessful&&d.isSubmitSuccessful,isSubmitting:!1})},at=(a,b)=>as(I(a)?a(t):a,b),au=a=>{d={...d,...a}},av={control:{register:ap,unregister:an,getFieldState:ak,handleSubmit:ar,setError:al,_subscribe:am,_runSchema:P,_focusError:aq,_getWatch:U,_getDirty:T,_setValid:D,_setFieldArray:(a,b=[],e,f,g=!0,h=!0)=>{if(f&&e&&!c.disabled){if(u.action=!0,h&&Array.isArray(p(l,a))){let b=e(p(l,a),f.argA,f.argB);g&&q(l,a,b)}if(h&&Array.isArray(p(d.errors,a))){let b,c=e(p(d.errors,a),f.argA,f.argB);g&&q(d.errors,a,c),n(p(b=d.errors,a)).length||L(b,a)}if((x.touchedFields||y.touchedFields)&&h&&Array.isArray(p(d.touchedFields,a))){let b=e(p(d.touchedFields,a),f.argA,f.argB);g&&q(d.touchedFields,a,b)}(x.dirtyFields||y.dirtyFields)&&(d.dirtyFields=O(o,t)),z.state.next({name:a,isDirty:T(a,b),dirtyFields:d.dirtyFields,errors:d.errors,isValid:d.isValid})}else q(t,a,b)},_setDisabledField:ao,_setErrors:a=>{d.errors=a,z.state.next({errors:d.errors,isValid:!1})},_getFieldArray:a=>n(p(u.mount?t:o,a,c.shouldUnregister?p(o,a,[]):[])),_reset:as,_resetDefaultValues:()=>I(c.defaultValues)&&c.defaultValues().then(a=>{at(a,c.resetOptions),z.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let a of v.unMount){let b=p(l,a);b&&(b._f.refs?b._f.refs.every(a=>!K(a)):!K(b._f.ref))&&an(a)}v.unMount=new Set},_disableForm:a=>{"boolean"==typeof a&&(z.state.next({disabled:a}),_(l,(b,c)=>{let d=p(l,c);d&&(b.disabled=d._f.disabled||a,Array.isArray(d._f.refs)&&d._f.refs.forEach(b=>{b.disabled=d._f.disabled||a}))},0,!1))},_subjects:z,_proxyFormState:x,get _fields(){return l},get _formValues(){return t},get _state(){return u},set _state(value){u=value},get _defaultValues(){return o},get _names(){return v},set _names(value){v=value},get _formState(){return d},get _options(){return c},set _options(value){c={...c,...value}}},subscribe:a=>(u.mount=!0,y={...y,...a.formState},am({...a,formState:y})),trigger:ai,register:ap,handleSubmit:ar,watch:(a,b)=>I(a)?z.state.subscribe({next:c=>"values"in c&&a(U(void 0,b),c)}):U(a,b,!0),setValue:ad,getValues:aj,reset:at,resetField:(a,b={})=>{p(l,a)&&(m(b.defaultValue)?ad(a,k(p(o,a))):(ad(a,b.defaultValue),q(o,a,k(b.defaultValue))),b.keepTouched||L(d.touchedFields,a),b.keepDirty||(L(d.dirtyFields,a),d.isDirty=b.defaultValue?T(a,k(p(o,a))):T()),!b.keepError&&(L(d.errors,a),x.isValid&&D()),z.state.next({...d}))},clearErrors:a=>{a&&F(a).forEach(a=>L(d.errors,a)),z.state.next({errors:a?d.errors:{}})},unregister:an,setError:al,setFocus:(a,b={})=>{let c=p(l,a),d=c&&c._f;if(d){let a=d.refs?d.refs[0]:d.ref;a.focus&&(a.focus(),b.shouldSelect&&I(a.select)&&a.select())}},getFieldState:ak};return{...av,formControl:av}}(a);b.current={...d,formState:l}}let t=b.current.control;return t._options=a,y(()=>{let a=t._subscribe({formState:t._proxyFormState,callback:()=>o({...t._formState}),reRenderRoot:!0});return o(a=>({...a,isReady:!0})),t._formState.isReady=!0,a},[t]),d.useEffect(()=>t._disableForm(a.disabled),[t,a.disabled]),d.useEffect(()=>{a.mode&&(t._options.mode=a.mode),a.reValidateMode&&(t._options.reValidateMode=a.reValidateMode)},[t,a.mode,a.reValidateMode]),d.useEffect(()=>{a.errors&&(t._setErrors(a.errors),t._focusError())},[t,a.errors]),d.useEffect(()=>{a.shouldUnregister&&t._subjects.state.next({values:t._getWatch()})},[t,a.shouldUnregister]),d.useEffect(()=>{if(t._proxyFormState.isDirty){let a=t._getDirty();a!==l.isDirty&&t._subjects.state.next({isDirty:a})}},[t,l.isDirty]),d.useEffect(()=>{a.values&&!C(a.values,c.current)?(t._reset(a.values,{keepFieldsRef:!0,...t._options.resetOptions}),c.current=a.values,o(a=>({...a}))):t._resetDefaultValues()},[t,a.values]),d.useEffect(()=>{t._state.mount||(t._setValid(),t._state.mount=!0),t._state.watch&&(t._state.watch=!1,t._subjects.state.next({...t._formState})),t._removeUnmounted()}),b.current.formState=x(l,t),b.current}},37566:(a,b,c)=>{c.d(b,{EB:()=>ba,Ik:()=>bz,Yj:()=>a9});var d=c(38291);let e=/^[cC][^\s-]{8,}$/,f=/^[0-9a-z]+$/,g=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,h=/^[0-9a-vA-V]{20}$/,i=/^[A-Za-z0-9]{27}$/,j=/^[a-zA-Z0-9_-]{21}$/,k=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,l=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,m=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,n=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,o=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,p=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,q=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,r=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,s=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,t=/^[A-Za-z0-9_-]*$/,u=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,v=/^\+(?:[0-9]){6,14}[0-9]$/,w="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",x=RegExp(`^${w}$`);function y(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}let z=/^[^A-Z]*$/,A=/^[^a-z]*$/;var B=c(84324);let C=d.xI("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),D=d.xI("$ZodCheckMaxLength",(a,b)=>{var c;C.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!B.cl(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=B.Rc(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),E=d.xI("$ZodCheckMinLength",(a,b)=>{var c;C.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!B.cl(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=B.Rc(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),F=d.xI("$ZodCheckLengthEquals",(a,b)=>{var c;C.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!B.cl(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=B.Rc(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),G=d.xI("$ZodCheckStringFormat",(a,b)=>{var c,d;C.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),H=d.xI("$ZodCheckRegex",(a,b)=>{G.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),I=d.xI("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=z),G.init(a,b)}),J=d.xI("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=A),G.init(a,b)}),K=d.xI("$ZodCheckIncludes",(a,b)=>{C.init(a,b);let c=B.$f(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),L=d.xI("$ZodCheckStartsWith",(a,b)=>{C.init(a,b);let c=RegExp(`^${B.$f(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),M=d.xI("$ZodCheckEndsWith",(a,b)=>{C.init(a,b);let c=RegExp(`.*${B.$f(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}}),N=d.xI("$ZodCheckOverwrite",(a,b)=>{C.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});class O{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}var P=c(63865);let Q={major:4,minor:0,patch:10},R=d.xI("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=Q;let e=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&e.unshift(a),e))for(let c of b._zod.onattach)c(a);if(0===e.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let e,f=B.QH(a);for(let g of b){if(g._zod.def.when){if(!g._zod.def.when(a))continue}else if(f)continue;let b=a.issues.length,h=g._zod.check(a);if(h instanceof Promise&&c?.async===!1)throw new d.GT;if(e||h instanceof Promise)e=(e??Promise.resolve()).then(async()=>{await h,a.issues.length!==b&&(f||(f=B.QH(a,b)))});else{if(a.issues.length===b)continue;f||(f=B.QH(a,b))}}return e?e.then(()=>a):a};a._zod.run=(c,f)=>{let g=a._zod.parse(c,f);if(g instanceof Promise){if(!1===f.async)throw new d.GT;return g.then(a=>b(a,e,f))}return b(g,e,f)}}a["~standard"]={validate:b=>{try{let c=(0,P.xL)(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return(0,P.bp)(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),S=d.xI("$ZodString",(a,b)=>{R.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??(a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)})(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),T=d.xI("$ZodStringFormat",(a,b)=>{G.init(a,b),S.init(a,b)}),U=d.xI("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=l),T.init(a,b)}),V=d.xI("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=m(a))}else b.pattern??(b.pattern=m());T.init(a,b)}),W=d.xI("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=n),T.init(a,b)}),X=d.xI("$ZodURL",(a,b)=>{T.init(a,b),a._zod.check=c=>{try{let d=c.value.trim(),e=new URL(d);b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:u.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),b.normalize?c.value=e.href:c.value=d;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),Y=d.xI("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),T.init(a,b)}),Z=d.xI("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=j),T.init(a,b)}),$=d.xI("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=e),T.init(a,b)}),_=d.xI("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=f),T.init(a,b)}),aa=d.xI("$ZodULID",(a,b)=>{b.pattern??(b.pattern=g),T.init(a,b)}),ab=d.xI("$ZodXID",(a,b)=>{b.pattern??(b.pattern=h),T.init(a,b)}),ac=d.xI("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=i),T.init(a,b)}),ad=d.xI("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=function(a){let b=y({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-]\\d{2}:\\d{2})");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${w}T(?:${d})$`)}(b)),T.init(a,b)}),ae=d.xI("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=x),T.init(a,b)}),af=d.xI("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=RegExp(`^${y(b)}$`)),T.init(a,b)}),ag=d.xI("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=k),T.init(a,b)}),ah=d.xI("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=o),T.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),ai=d.xI("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=p),T.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),aj=d.xI("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=q),T.init(a,b)}),ak=d.xI("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=r),T.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function al(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let am=d.xI("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=s),T.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{al(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}}),an=d.xI("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=t),T.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{!function(a){if(!t.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return al(b.padEnd(4*Math.ceil(b.length/4),"="))}(c.value)&&c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),ao=d.xI("$ZodE164",(a,b)=>{b.pattern??(b.pattern=v),T.init(a,b)}),ap=d.xI("$ZodJWT",(a,b)=>{T.init(a,b),a._zod.check=c=>{!function(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}(c.value,b.alg)&&c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),aq=d.xI("$ZodUnknown",(a,b)=>{R.init(a,b),a._zod.parse=a=>a}),ar=d.xI("$ZodNever",(a,b)=>{R.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)});function as(a,b,c){a.issues.length&&b.issues.push(...B.lQ(c,a.issues)),b.value[c]=a.value}let at=d.xI("$ZodArray",(a,b)=>{R.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>as(b,c,a))):as(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function au(a,b,c,d){a.issues.length&&b.issues.push(...B.lQ(c,a.issues)),void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}let av=d.xI("$ZodObject",(a,b)=>{let c,e;R.init(a,b);let f=B.PO(()=>{let a=Object.keys(b.shape);for(let c of a)if(!(b.shape[c]instanceof R))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=B.NM(b.shape);return{shape:b.shape,keys:a,keySet:new Set(a),numKeys:a.length,optionalKeys:new Set(c)}});B.gJ(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let g=B.Gv,h=!d.cr.jitless,i=B.hI,j=h&&i.value,k=b.catchall;a._zod.parse=(d,i)=>{e??(e=f.value);let l=d.value;if(!g(l))return d.issues.push({expected:"object",code:"invalid_type",input:l,inst:a}),d;let m=[];if(h&&j&&i?.async===!1&&!0!==i.jitless)c||(c=(a=>{let b=new O(["shape","payload","ctx"]),c=f.value,d=a=>{let b=B.UQ(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let e=Object.create(null),g=0;for(let a of c.keys)e[a]=`key_${g++}`;for(let a of(b.write("const newResult = {}"),c.keys)){let c=e[a],f=B.UQ(a);b.write(`const ${c} = ${d(a)};`),b.write(`
        if (${c}.issues.length) {
          payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${f}, ...iss.path] : [${f}]
          })));
        }
        
        if (${c}.value === undefined) {
          if (${f} in input) {
            newResult[${f}] = undefined;
          }
        } else {
          newResult[${f}] = ${c}.value;
        }
      `)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),d=c(d,i);else{d.value={};let a=e.shape;for(let b of e.keys){let c=a[b]._zod.run({value:l[b],issues:[]},i);c instanceof Promise?m.push(c.then(a=>au(a,d,b,l))):au(c,d,b,l)}}if(!k)return m.length?Promise.all(m).then(()=>d):d;let n=[],o=e.keySet,p=k._zod,q=p.def.type;for(let a of Object.keys(l)){if(o.has(a))continue;if("never"===q){n.push(a);continue}let b=p.run({value:l[a],issues:[]},i);b instanceof Promise?m.push(b.then(b=>au(b,d,a,l))):au(b,d,a,l)}return(n.length&&d.issues.push({code:"unrecognized_keys",keys:n,input:l,inst:a}),m.length)?Promise.all(m).then(()=>d):d}});function aw(a,b,c,e){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;let f=a.filter(a=>!B.QH(a));return 1===f.length?(b.value=f[0].value,f[0]):(b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>B.iR(a,e,d.$W())))}),b)}let ax=d.xI("$ZodUnion",(a,b)=>{R.init(a,b),B.gJ(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),B.gJ(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),B.gJ(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),B.gJ(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>B.p6(a.source)).join("|")})$`)}}),a._zod.parse=(c,d)=>{let e=!1,f=[];for(let a of b.options){let b=a._zod.run({value:c.value,issues:[]},d);if(b instanceof Promise)f.push(b),e=!0;else{if(0===b.issues.length)return b;f.push(b)}}return e?Promise.all(f).then(b=>aw(b,c,a,d)):aw(f,c,a,d)}}),ay=d.xI("$ZodIntersection",(a,b)=>{R.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>az(a,b,c)):az(a,e,f)}});function az(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),B.QH(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(B.Qd(b)&&B.Qd(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let aA=d.xI("$ZodEnum",(a,b)=>{R.init(a,b);let c=B.w5(b.entries),d=new Set(c);a._zod.values=d,a._zod.pattern=RegExp(`^(${c.filter(a=>B.qQ.has(typeof a)).map(a=>"string"==typeof a?B.$f(a):a.toString()).join("|")})$`),a._zod.parse=(b,e)=>{let f=b.value;return d.has(f)||b.issues.push({code:"invalid_value",values:c,input:f,inst:a}),b}}),aB=d.xI("$ZodTransform",(a,b)=>{R.init(a,b),a._zod.parse=(a,c)=>{let e=b.transform(a.value,a);if(c.async)return(e instanceof Promise?e:Promise.resolve(e)).then(b=>(a.value=b,a));if(e instanceof Promise)throw new d.GT;return a.value=e,a}}),aC=d.xI("$ZodOptional",(a,b)=>{R.init(a,b),a._zod.optin="optional",a._zod.optout="optional",B.gJ(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),B.gJ(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${B.p6(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>"optional"===b.innerType._zod.optin?b.innerType._zod.run(a,c):void 0===a.value?a:b.innerType._zod.run(a,c)}),aD=d.xI("$ZodNullable",(a,b)=>{R.init(a,b),B.gJ(a._zod,"optin",()=>b.innerType._zod.optin),B.gJ(a._zod,"optout",()=>b.innerType._zod.optout),B.gJ(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${B.p6(a.source)}|null)$`):void 0}),B.gJ(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),aE=d.xI("$ZodDefault",(a,b)=>{R.init(a,b),a._zod.optin="optional",B.gJ(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>aF(a,b)):aF(d,b)}});function aF(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let aG=d.xI("$ZodPrefault",(a,b)=>{R.init(a,b),a._zod.optin="optional",B.gJ(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>(void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),aH=d.xI("$ZodNonOptional",(a,b)=>{R.init(a,b),B.gJ(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>aI(b,a)):aI(e,a)}});function aI(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let aJ=d.xI("$ZodCatch",(a,b)=>{R.init(a,b),B.gJ(a._zod,"optin",()=>b.innerType._zod.optin),B.gJ(a._zod,"optout",()=>b.innerType._zod.optout),B.gJ(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{let e=b.innerType._zod.run(a,c);return e instanceof Promise?e.then(e=>(a.value=e.value,e.issues.length&&(a.value=b.catchValue({...a,error:{issues:e.issues.map(a=>B.iR(a,c,d.$W()))},input:a.value}),a.issues=[]),a)):(a.value=e.value,e.issues.length&&(a.value=b.catchValue({...a,error:{issues:e.issues.map(a=>B.iR(a,c,d.$W()))},input:a.value}),a.issues=[]),a)}}),aK=d.xI("$ZodPipe",(a,b)=>{R.init(a,b),B.gJ(a._zod,"values",()=>b.in._zod.values),B.gJ(a._zod,"optin",()=>b.in._zod.optin),B.gJ(a._zod,"optout",()=>b.out._zod.optout),B.gJ(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>aL(a,b,c)):aL(d,b,c)}});function aL(a,b,c){return a.issues.length?a:b.out._zod.run({value:a.value,issues:a.issues},c)}let aM=d.xI("$ZodReadonly",(a,b)=>{R.init(a,b),B.gJ(a._zod,"propValues",()=>b.innerType._zod.propValues),B.gJ(a._zod,"values",()=>b.innerType._zod.values),B.gJ(a._zod,"optin",()=>b.innerType._zod.optin),B.gJ(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(aN):aN(d)}});function aN(a){return a.value=Object.freeze(a.value),a}let aO=d.xI("$ZodCustom",(a,b)=>{C.init(a,b),R.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>aP(b,c,d,a));aP(e,c,d,a)}});function aP(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(B.sn(a))}}Symbol("ZodOutput"),Symbol("ZodInput");class aQ{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};delete c.id;let d={...c,...this._map.get(a)};return Object.keys(d).length?d:void 0}return this._map.get(a)}has(a){return this._map.has(a)}}let aR=new aQ;function aS(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...B.A2(b)})}function aT(a,b){return new D({check:"max_length",...B.A2(b),maximum:a})}function aU(a,b){return new E({check:"min_length",...B.A2(b),minimum:a})}function aV(a,b){return new F({check:"length_equals",...B.A2(b),length:a})}function aW(a){return new N({check:"overwrite",tx:a})}let aX=d.xI("ZodISODateTime",(a,b)=>{ad.init(a,b),ba.init(a,b)}),aY=d.xI("ZodISODate",(a,b)=>{ae.init(a,b),ba.init(a,b)}),aZ=d.xI("ZodISOTime",(a,b)=>{af.init(a,b),ba.init(a,b)}),a$=d.xI("ZodISODuration",(a,b)=>{ag.init(a,b),ba.init(a,b)});var a_=c(26499);let a0=(a,b)=>{a_.a$.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>a_.Wk(a,b)},flatten:{value:b=>a_.JM(a,b)},addIssue:{value:b=>{a.issues.push(b),a.message=JSON.stringify(a.issues,B.k8,2)}},addIssues:{value:b=>{a.issues.push(...b),a.message=JSON.stringify(a.issues,B.k8,2)}},isEmpty:{get:()=>0===a.issues.length}})};d.xI("ZodError",a0);let a1=d.xI("ZodError",a0,{Parent:Error}),a2=P.Tj(a1),a3=P.Rb(a1),a4=P.Od(a1),a5=P.wG(a1),a6=d.xI("ZodType",(a,b)=>(R.init(a,b),a.def=b,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>B.o8(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>a2(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>a4(a,b,c),a.parseAsync=async(b,c)=>a3(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>a5(a,b,c),a.spa=a.safeParseAsync,a.refine=(b,c)=>a.check(function(a,b={}){return new bP({type:"custom",check:"custom",fn:a,...B.A2(b)})}(b,c)),a.superRefine=b=>a.check(function(a){let b=function(a){let b=new C({check:"custom"});return b._zod.check=a,b}(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(B.sn(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(B.sn(a)))},a(c.value,c)));return b}(b)),a.overwrite=b=>a.check(aW(b)),a.optional=()=>bF(a),a.nullable=()=>bH(a),a.nullish=()=>bF(bH(a)),a.nonoptional=b=>{var c,d;return c=a,d=b,new bK({type:"nonoptional",innerType:c,...B.A2(d)})},a.array=()=>(function(a,b){return new bx({type:"array",element:a,...B.A2(b)})})(a),a.or=b=>new bA({type:"union",options:[a,b],...B.A2(void 0)}),a.and=b=>new bB({type:"intersection",left:a,right:b}),a.transform=b=>bN(a,new bD({type:"transform",transform:b})),a.default=b=>(function(a,b){return new bI({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.prefault=b=>(function(a,b){return new bJ({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.catch=b=>(function(a,b){return new bL({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})})(a,b),a.pipe=b=>bN(a,b),a.readonly=()=>new bO({type:"readonly",innerType:a}),a.describe=b=>{let c=a.clone();return aR.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>aR.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return aR.get(a);let c=a.clone();return aR.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),a7=d.xI("_ZodString",(a,b)=>{S.init(a,b),a6.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(function(a,b){return new H({check:"string_format",format:"regex",...B.A2(b),pattern:a})}(...b)),a.includes=(...b)=>a.check(function(a,b){return new K({check:"string_format",format:"includes",...B.A2(b),includes:a})}(...b)),a.startsWith=(...b)=>a.check(function(a,b){return new L({check:"string_format",format:"starts_with",...B.A2(b),prefix:a})}(...b)),a.endsWith=(...b)=>a.check(function(a,b){return new M({check:"string_format",format:"ends_with",...B.A2(b),suffix:a})}(...b)),a.min=(...b)=>a.check(aU(...b)),a.max=(...b)=>a.check(aT(...b)),a.length=(...b)=>a.check(aV(...b)),a.nonempty=(...b)=>a.check(aU(1,...b)),a.lowercase=b=>a.check(new I({check:"string_format",format:"lowercase",...B.A2(b)})),a.uppercase=b=>a.check(new J({check:"string_format",format:"uppercase",...B.A2(b)})),a.trim=()=>a.check(aW(a=>a.trim())),a.normalize=(...b)=>a.check(function(a){return aW(b=>b.normalize(a))}(...b)),a.toLowerCase=()=>a.check(aW(a=>a.toLowerCase())),a.toUpperCase=()=>a.check(aW(a=>a.toUpperCase()))}),a8=d.xI("ZodString",(a,b)=>{S.init(a,b),a7.init(a,b),a.email=b=>a.check(new bb({type:"string",format:"email",check:"string_format",abort:!1,...B.A2(b)})),a.url=b=>a.check(new be({type:"string",format:"url",check:"string_format",abort:!1,...B.A2(b)})),a.jwt=b=>a.check(new bt({type:"string",format:"jwt",check:"string_format",abort:!1,...B.A2(b)})),a.emoji=b=>a.check(new bf({type:"string",format:"emoji",check:"string_format",abort:!1,...B.A2(b)})),a.guid=b=>a.check(aS(bc,b)),a.uuid=b=>a.check(new bd({type:"string",format:"uuid",check:"string_format",abort:!1,...B.A2(b)})),a.uuidv4=b=>a.check(new bd({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...B.A2(b)})),a.uuidv6=b=>a.check(new bd({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...B.A2(b)})),a.uuidv7=b=>a.check(new bd({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...B.A2(b)})),a.nanoid=b=>a.check(new bg({type:"string",format:"nanoid",check:"string_format",abort:!1,...B.A2(b)})),a.guid=b=>a.check(aS(bc,b)),a.cuid=b=>a.check(new bh({type:"string",format:"cuid",check:"string_format",abort:!1,...B.A2(b)})),a.cuid2=b=>a.check(new bi({type:"string",format:"cuid2",check:"string_format",abort:!1,...B.A2(b)})),a.ulid=b=>a.check(new bj({type:"string",format:"ulid",check:"string_format",abort:!1,...B.A2(b)})),a.base64=b=>a.check(new bq({type:"string",format:"base64",check:"string_format",abort:!1,...B.A2(b)})),a.base64url=b=>a.check(new br({type:"string",format:"base64url",check:"string_format",abort:!1,...B.A2(b)})),a.xid=b=>a.check(new bk({type:"string",format:"xid",check:"string_format",abort:!1,...B.A2(b)})),a.ksuid=b=>a.check(new bl({type:"string",format:"ksuid",check:"string_format",abort:!1,...B.A2(b)})),a.ipv4=b=>a.check(new bm({type:"string",format:"ipv4",check:"string_format",abort:!1,...B.A2(b)})),a.ipv6=b=>a.check(new bn({type:"string",format:"ipv6",check:"string_format",abort:!1,...B.A2(b)})),a.cidrv4=b=>a.check(new bo({type:"string",format:"cidrv4",check:"string_format",abort:!1,...B.A2(b)})),a.cidrv6=b=>a.check(new bp({type:"string",format:"cidrv6",check:"string_format",abort:!1,...B.A2(b)})),a.e164=b=>a.check(new bs({type:"string",format:"e164",check:"string_format",abort:!1,...B.A2(b)})),a.datetime=b=>a.check(function(a){return new aX({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...B.A2(a)})}(b)),a.date=b=>a.check(function(a){return new aY({type:"string",format:"date",check:"string_format",...B.A2(a)})}(b)),a.time=b=>a.check(function(a){return new aZ({type:"string",format:"time",check:"string_format",precision:null,...B.A2(a)})}(b)),a.duration=b=>a.check(function(a){return new a$({type:"string",format:"duration",check:"string_format",...B.A2(a)})}(b))});function a9(a){return new a8({type:"string",...B.A2(a)})}let ba=d.xI("ZodStringFormat",(a,b)=>{T.init(a,b),a7.init(a,b)}),bb=d.xI("ZodEmail",(a,b)=>{W.init(a,b),ba.init(a,b)}),bc=d.xI("ZodGUID",(a,b)=>{U.init(a,b),ba.init(a,b)}),bd=d.xI("ZodUUID",(a,b)=>{V.init(a,b),ba.init(a,b)}),be=d.xI("ZodURL",(a,b)=>{X.init(a,b),ba.init(a,b)}),bf=d.xI("ZodEmoji",(a,b)=>{Y.init(a,b),ba.init(a,b)}),bg=d.xI("ZodNanoID",(a,b)=>{Z.init(a,b),ba.init(a,b)}),bh=d.xI("ZodCUID",(a,b)=>{$.init(a,b),ba.init(a,b)}),bi=d.xI("ZodCUID2",(a,b)=>{_.init(a,b),ba.init(a,b)}),bj=d.xI("ZodULID",(a,b)=>{aa.init(a,b),ba.init(a,b)}),bk=d.xI("ZodXID",(a,b)=>{ab.init(a,b),ba.init(a,b)}),bl=d.xI("ZodKSUID",(a,b)=>{ac.init(a,b),ba.init(a,b)}),bm=d.xI("ZodIPv4",(a,b)=>{ah.init(a,b),ba.init(a,b)}),bn=d.xI("ZodIPv6",(a,b)=>{ai.init(a,b),ba.init(a,b)}),bo=d.xI("ZodCIDRv4",(a,b)=>{aj.init(a,b),ba.init(a,b)}),bp=d.xI("ZodCIDRv6",(a,b)=>{ak.init(a,b),ba.init(a,b)}),bq=d.xI("ZodBase64",(a,b)=>{am.init(a,b),ba.init(a,b)}),br=d.xI("ZodBase64URL",(a,b)=>{an.init(a,b),ba.init(a,b)}),bs=d.xI("ZodE164",(a,b)=>{ao.init(a,b),ba.init(a,b)}),bt=d.xI("ZodJWT",(a,b)=>{ap.init(a,b),ba.init(a,b)}),bu=d.xI("ZodUnknown",(a,b)=>{aq.init(a,b),a6.init(a,b)});function bv(){return new bu({type:"unknown"})}let bw=d.xI("ZodNever",(a,b)=>{ar.init(a,b),a6.init(a,b)}),bx=d.xI("ZodArray",(a,b)=>{at.init(a,b),a6.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(aU(b,c)),a.nonempty=b=>a.check(aU(1,b)),a.max=(b,c)=>a.check(aT(b,c)),a.length=(b,c)=>a.check(aV(b,c)),a.unwrap=()=>a.element}),by=d.xI("ZodObject",(a,b)=>{av.init(a,b),a6.init(a,b),B.gJ(a,"shape",()=>b.shape),a.keyof=()=>(function(a,b){return new bC({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...B.A2(void 0)})})(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:bv()}),a.loose=()=>a.clone({...a._zod.def,catchall:bv()}),a.strict=()=>a.clone({...a._zod.def,catchall:function(a){var b;return b=void 0,new bw({type:"never",...B.A2(b)})}()}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>B.X$(a,b),a.merge=b=>B.h1(a,b),a.pick=b=>B.Up(a,b),a.omit=b=>B.cJ(a,b),a.partial=(...b)=>B.OH(bE,a,b[0]),a.required=(...b)=>B.mw(bK,a,b[0])});function bz(a,b){return new by({type:"object",get shape(){return B.Vy(this,"shape",{...a}),this.shape},...B.A2(b)})}let bA=d.xI("ZodUnion",(a,b)=>{ax.init(a,b),a6.init(a,b),a.options=b.options}),bB=d.xI("ZodIntersection",(a,b)=>{ay.init(a,b),a6.init(a,b)}),bC=d.xI("ZodEnum",(a,b)=>{aA.init(a,b),a6.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new bC({...b,checks:[],...B.A2(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new bC({...b,checks:[],...B.A2(d),entries:e})}}),bD=d.xI("ZodTransform",(a,b)=>{aB.init(a,b),a6.init(a,b),a._zod.parse=(c,d)=>{c.addIssue=d=>{"string"==typeof d?c.issues.push(B.sn(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),c.issues.push(B.sn(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}}),bE=d.xI("ZodOptional",(a,b)=>{aC.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType});function bF(a){return new bE({type:"optional",innerType:a})}let bG=d.xI("ZodNullable",(a,b)=>{aD.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType});function bH(a){return new bG({type:"nullable",innerType:a})}let bI=d.xI("ZodDefault",(a,b)=>{aE.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap}),bJ=d.xI("ZodPrefault",(a,b)=>{aG.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType}),bK=d.xI("ZodNonOptional",(a,b)=>{aH.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType}),bL=d.xI("ZodCatch",(a,b)=>{aJ.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap}),bM=d.xI("ZodPipe",(a,b)=>{aK.init(a,b),a6.init(a,b),a.in=b.in,a.out=b.out});function bN(a,b){return new bM({type:"pipe",in:a,out:b})}let bO=d.xI("ZodReadonly",(a,b)=>{aM.init(a,b),a6.init(a,b),a.unwrap=()=>a._zod.def.innerType}),bP=d.xI("ZodCustom",(a,b)=>{aO.init(a,b),a6.init(a,b)})},38291:(a,b,c)=>{function d(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}c.d(b,{$W:()=>g,GT:()=>e,cr:()=>f,xI:()=>d}),Object.freeze({status:"aborted"}),Symbol("zod_brand");class e extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let f={};function g(a){return a&&Object.assign(f,a),f}},48730:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58887:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63442:(a,b,c)=>{c.d(b,{u:()=>m});var d=c(27605);let e=(a,b,c)=>{if(a&&"reportValidity"in a){let e=(0,d.Jt)(c,b);a.setCustomValidity(e&&e.message||""),a.reportValidity()}},f=(a,b)=>{for(let c in b.fields){let d=b.fields[c];d&&d.ref&&"reportValidity"in d.ref?e(d.ref,c,a):d&&d.refs&&d.refs.forEach(b=>e(b,c,a))}},g=(a,b)=>{b.shouldUseNativeValidation&&f(a,b);let c={};for(let e in a){let f=(0,d.Jt)(b.fields,e),g=Object.assign(a[e]||{},{ref:f&&f.ref});if(h(b.names||Object.keys(a),e)){let a=Object.assign({},(0,d.Jt)(c,e));(0,d.hZ)(a,"root",g),(0,d.hZ)(c,e,a)}else(0,d.hZ)(c,e,g)}return c},h=(a,b)=>{let c=i(b);return a.some(a=>i(a).match(`^${c}\\.\\d+`))};function i(a){return a.replace(/\]|\[/g,"")}var j=c(63865),k=c(26499);function l(a,b){try{var c=a()}catch(a){return b(a)}return c&&c.then?c.then(void 0,b):c}function m(a,b,c){if(void 0===c&&(c={}),"_def"in a&&"object"==typeof a._def&&"typeName"in a._def)return function(e,h,i){try{return Promise.resolve(l(function(){return Promise.resolve(a["sync"===c.mode?"parse":"parseAsync"](e,b)).then(function(a){return i.shouldUseNativeValidation&&f({},i),{errors:{},values:c.raw?Object.assign({},e):a}})},function(a){if(Array.isArray(null==a?void 0:a.issues))return{values:{},errors:g(function(a,b){for(var c={};a.length;){var e=a[0],f=e.code,g=e.message,h=e.path.join(".");if(!c[h])if("unionErrors"in e){var i=e.unionErrors[0].errors[0];c[h]={message:i.message,type:i.code}}else c[h]={message:g,type:f};if("unionErrors"in e&&e.unionErrors.forEach(function(b){return b.errors.forEach(function(b){return a.push(b)})}),b){var j=c[h].types,k=j&&j[e.code];c[h]=(0,d.Gb)(h,b,c,f,k?[].concat(k,e.message):e.message)}a.shift()}return c}(a.errors,!i.shouldUseNativeValidation&&"all"===i.criteriaMode),i)};throw a}))}catch(a){return Promise.reject(a)}};if("_zod"in a&&"object"==typeof a._zod)return function(e,h,i){try{return Promise.resolve(l(function(){return Promise.resolve(("sync"===c.mode?j.qg:j.EJ)(a,e,b)).then(function(a){return i.shouldUseNativeValidation&&f({},i),{errors:{},values:c.raw?Object.assign({},e):a}})},function(a){if(a instanceof k.a$)return{values:{},errors:g(function(a,b){for(var c={};a.length;){var e=a[0],f=e.code,g=e.message,h=e.path.join(".");if(!c[h])if("invalid_union"===e.code){var i=e.errors[0][0];c[h]={message:i.message,type:i.code}}else c[h]={message:g,type:f};if("invalid_union"===e.code&&e.errors.forEach(function(b){return b.forEach(function(b){return a.push(b)})}),b){var j=c[h].types,k=j&&j[e.code];c[h]=(0,d.Gb)(h,b,c,f,k?[].concat(k,e.message):e.message)}a.shift()}return c}(a.issues,!i.shouldUseNativeValidation&&"all"===i.criteriaMode),i)};throw a}))}catch(a){return Promise.reject(a)}};throw Error("Invalid input: not a Zod schema")}},63865:(a,b,c)=>{c.d(b,{EJ:()=>j,Od:()=>k,Rb:()=>i,Tj:()=>g,bp:()=>n,qg:()=>h,wG:()=>m,xL:()=>l});var d=c(38291),e=c(26499),f=c(84324);let g=a=>(b,c,e,g)=>{let h=e?Object.assign(e,{async:!1}):{async:!1},i=b._zod.run({value:c,issues:[]},h);if(i instanceof Promise)throw new d.GT;if(i.issues.length){let b=new(g?.Err??a)(i.issues.map(a=>f.iR(a,h,d.$W())));throw f.gx(b,g?.callee),b}return i.value},h=g(e.Kd),i=a=>async(b,c,e,g)=>{let h=e?Object.assign(e,{async:!0}):{async:!0},i=b._zod.run({value:c,issues:[]},h);if(i instanceof Promise&&(i=await i),i.issues.length){let b=new(g?.Err??a)(i.issues.map(a=>f.iR(a,h,d.$W())));throw f.gx(b,g?.callee),b}return i.value},j=i(e.Kd),k=a=>(b,c,g)=>{let h=g?{...g,async:!1}:{async:!1},i=b._zod.run({value:c,issues:[]},h);if(i instanceof Promise)throw new d.GT;return i.issues.length?{success:!1,error:new(a??e.a$)(i.issues.map(a=>f.iR(a,h,d.$W())))}:{success:!0,data:i.value}},l=k(e.Kd),m=a=>async(b,c,e)=>{let g=e?Object.assign(e,{async:!0}):{async:!0},h=b._zod.run({value:c,issues:[]},g);return h instanceof Promise&&(h=await h),h.issues.length?{success:!1,error:new a(h.issues.map(a=>f.iR(a,g,d.$W())))}:{success:!0,data:h.value}},n=m(e.Kd)},78148:(a,b,c)=>{c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},78272:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84324:(a,b,c)=>{function d(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}function e(a,b){return"bigint"==typeof b?b.toString():b}function f(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function g(a){return null==a}function h(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function i(a,b,c){Object.defineProperty(a,b,{get(){{let d=c();return a[b]=d,d}},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function j(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function k(...a){let b={};for(let c of a)Object.assign(b,Object.getOwnPropertyDescriptors(c));return Object.defineProperties({},b)}function l(a){return JSON.stringify(a)}c.d(b,{$f:()=>r,A2:()=>t,Gv:()=>n,NM:()=>u,OH:()=>z,PO:()=>f,QH:()=>B,Qd:()=>p,Rc:()=>F,UQ:()=>l,Up:()=>v,Vy:()=>j,X$:()=>x,cJ:()=>w,cl:()=>g,gJ:()=>i,gx:()=>m,h1:()=>y,hI:()=>o,iR:()=>E,k8:()=>e,lQ:()=>C,mw:()=>A,o8:()=>s,p6:()=>h,qQ:()=>q,sn:()=>G,w5:()=>d});let m="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function n(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let o=f(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function p(a){if(!1===n(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==n(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}let q=new Set(["string","number","symbol"]);function r(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function s(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function t(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}function u(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}function v(a,b){let c=a._zod.def,d=k(a._zod.def,{get shape(){let a={};for(let d in b){if(!(d in c.shape))throw Error(`Unrecognized key: "${d}"`);b[d]&&(a[d]=c.shape[d])}return j(this,"shape",a),a},checks:[]});return s(a,d)}function w(a,b){let c=a._zod.def,d=k(a._zod.def,{get shape(){let d={...a._zod.def.shape};for(let a in b){if(!(a in c.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete d[a]}return j(this,"shape",d),d},checks:[]});return s(a,d)}function x(a,b){if(!p(b))throw Error("Invalid input to extend: expected a plain object");let c=k(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b};return j(this,"shape",c),c},checks:[]});return s(a,c)}function y(a,b){let c=k(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return j(this,"shape",c),c},get catchall(){return b._zod.def.catchall},checks:[]});return s(a,c)}function z(a,b,c){let d=k(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return j(this,"shape",e),e},checks:[]});return s(b,d)}function A(a,b,c){let d=k(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return j(this,"shape",e),e},checks:[]});return s(b,d)}function B(a,b=0){for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function C(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function D(a){return"string"==typeof a?a:a?.message}function E(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=D(a.inst?._zod.def?.error?.(a))??D(b?.error?.(a))??D(c.customError?.(a))??D(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function F(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function G(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE}};