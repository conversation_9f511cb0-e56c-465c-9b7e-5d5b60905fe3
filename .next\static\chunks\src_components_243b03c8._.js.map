{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { BookOpen, TrendingUp, Users, Award } from 'lucide-react';\n\nconst stats = [\n  { icon: BookOpen, value: '50+', label: 'Expert Articles' },\n  { icon: TrendingUp, value: '10K+', label: 'Monthly Readers' },\n  { icon: Users, value: '500+', label: 'Businesses Helped' },\n  { icon: Award, value: '5+', label: 'Years Experience' },\n];\n\nexport default function BlogHero() {\n  return (\n    <section className=\"relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <Image\n          src=\"https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80\"\n          alt=\"Digital marketing blog background\"\n          fill\n          className=\"object-cover\"\n          priority\n        />\n      </div>\n      \n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center mb-16\">\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\"\n          >\n            <BookOpen className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Digital Marketing Blog\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\"\n          >\n            Expert Insights for\n            <span className=\"block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent\">\n              Digital Success\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Stay ahead of the curve with our expert digital marketing insights, SEO tips, \n            social media strategies, and growth hacks specifically tailored for Nepali businesses. \n            Learn from real case studies and proven strategies.\n          </motion.p>\n\n          {/* Featured Topics */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-wrap justify-center gap-3 mb-12\"\n          >\n            {[\n              'Digital Marketing',\n              'SEO Tips',\n              'Social Media',\n              'Content Strategy',\n              'Case Studies',\n              'Nepal Market'\n            ].map((topic, index) => (\n              <motion.span\n                key={topic}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}\n                className=\"px-4 py-2 bg-white/80 backdrop-blur-sm text-brand-navy rounded-full text-sm font-medium border border-brand-navy/20 hover:bg-brand-navy hover:text-white transition-colors cursor-pointer\"\n              >\n                {topic}\n              </motion.span>\n            ))}\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-8\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"w-16 h-16 bg-gradient-to-br from-brand-navy to-brand-gold rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <stat.icon className=\"h-8 w-8 text-white\" />\n                </div>\n                <div className=\"text-2xl md:text-3xl font-bold text-brand-navy mb-2\">\n                  {stat.value}\n                </div>\n                <div className=\"text-gray-600 text-sm\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n\n        {/* Search Bar */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 1.0 }}\n          className=\"max-w-2xl mx-auto\"\n        >\n          <div className=\"bg-white rounded-2xl p-2 shadow-xl border border-gray-100\">\n            <div className=\"flex items-center\">\n              <input\n                type=\"text\"\n                placeholder=\"Search articles, tips, and insights...\"\n                className=\"flex-1 px-6 py-4 text-gray-700 placeholder-gray-400 bg-transparent border-none outline-none text-lg\"\n              />\n              <button className=\"px-8 py-4 bg-gradient-to-r from-brand-navy to-brand-gold text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105\">\n                Search\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,MAAM,iNAAA,CAAA,WAAQ;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzD;QAAE,MAAM,qNAAA,CAAA,aAAU;QAAE,OAAO;QAAQ,OAAO;IAAkB;IAC5D;QAAE,MAAM,uMAAA,CAAA,QAAK;QAAE,OAAO;QAAQ,OAAO;IAAoB;IACzD;QAAE,MAAM,uMAAA,CAAA,QAAK;QAAE,OAAO;QAAM,OAAO;IAAmB;CACvD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;oBACV,QAAQ;;;;;;;;;;;0BAKZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAKvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAqF;;;;;;;;;;;;0CAMvG,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CACX;;;;;;0CAOD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET;oCACC;oCACA;oCACA;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCAEV,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,WAAU;kDAET;uCANI;;;;;;;;;;0CAYX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,KAAK;;;;;;;uCAZ7C,KAAK,KAAK;;;;;;;;;;;;;;;;kCAmBvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAO,WAAU;kDAA2J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3L;KAvIwB", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-list.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Calendar, Clock, User, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\n\ninterface BlogPost {\n  id: string;\n  slug: string;\n  title: string;\n  excerpt: string;\n  author: {\n    name: string;\n    avatar: string;\n  };\n  publishedAt: string;\n  category: {\n    name: string;\n    slug: string;\n  };\n  tags: string[];\n  featuredImage: string;\n  readingTime: number;\n  featured: boolean;\n}\n\ninterface BlogListProps {\n  posts: BlogPost[];\n  totalPages: number;\n  currentPage: number;\n  searchParams: {\n    category?: string;\n    tag?: string;\n    search?: string;\n    page?: string;\n  };\n}\n\nexport default function BlogList({ posts, totalPages, currentPage, searchParams }: BlogListProps) {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  };\n\n  const createPageUrl = (page: number) => {\n    const params = new URLSearchParams();\n    if (searchParams.category) params.set('category', searchParams.category);\n    if (searchParams.tag) params.set('tag', searchParams.tag);\n    if (searchParams.search) params.set('search', searchParams.search);\n    if (page > 1) params.set('page', page.toString());\n    return `/blog${params.toString() ? `?${params.toString()}` : ''}`;\n  };\n\n  if (posts.length === 0) {\n    return (\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"max-w-md mx-auto\">\n            <div className=\"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <Calendar className=\"h-12 w-12 text-gray-400\" />\n            </div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">No Articles Found</h3>\n            <p className=\"text-gray-600 mb-6\">\n              We couldn't find any articles matching your criteria. Try adjusting your filters or search terms.\n            </p>\n            <Link href=\"/blog\">\n              <Button className=\"bg-brand-navy hover:bg-brand-navy-dark\">\n                View All Articles\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Results Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-12\"\n        >\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-2\">\n              Latest Articles\n            </h2>\n            <p className=\"text-gray-600\">\n              {posts.length} article{posts.length !== 1 ? 's' : ''} found\n              {searchParams.category && ` in ${searchParams.category}`}\n              {searchParams.search && ` for \"${searchParams.search}\"`}\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Regular Posts Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {posts.map((post, index) => (\n            <motion.div\n              key={post.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <Link href={`/blog/${post.slug}`} className=\"group block\">\n                <Card className=\"h-full overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2\">\n                  {/* Post Image */}\n                  <div className=\"relative h-48 overflow-hidden\">\n                    <Image\n                      src={post.featuredImage}\n                      alt={post.title}\n                      fill\n                      className=\"object-cover group-hover:scale-110 transition-transform duration-300\"\n                    />\n                    <div className=\"absolute top-4 left-4\">\n                      <Badge \n                        variant=\"outline\" \n                        className=\"bg-white/90 backdrop-blur-sm border-brand-navy text-brand-navy font-semibold\"\n                      >\n                        {post.category.name}\n                      </Badge>\n                    </div>\n                    {post.featured && (\n                      <div className=\"absolute top-4 right-4\">\n                        <Badge className=\"bg-brand-gold text-gray-900 font-semibold\">\n                          Featured\n                        </Badge>\n                      </div>\n                    )}\n                  </div>\n\n                  <CardContent className=\"p-6\">\n                    {/* Meta Info */}\n                    <div className=\"flex items-center text-sm text-gray-500 space-x-4 mb-3\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span>{formatDate(post.publishedAt)}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"h-4 w-4\" />\n                        <span>{post.readingTime} min</span>\n                      </div>\n                    </div>\n\n                    {/* Title */}\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-brand-navy transition-colors line-clamp-2\">\n                      {post.title}\n                    </h3>\n\n                    {/* Excerpt */}\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n\n                    {/* Author */}\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <Image\n                          src={post.author.avatar}\n                          alt={post.author.name}\n                          width={32}\n                          height={32}\n                          className=\"rounded-full\"\n                        />\n                        <div className=\"text-sm\">\n                          <div className=\"font-semibold text-gray-900\">{post.author.name}</div>\n                        </div>\n                      </div>\n                      <ArrowRight className=\"h-5 w-5 text-brand-navy group-hover:translate-x-1 transition-transform\" />\n                    </div>\n                  </CardContent>\n                </Card>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"flex justify-center items-center space-x-2\"\n          >\n            {/* Previous Button */}\n            {currentPage > 1 && (\n              <Link href={createPageUrl(currentPage - 1)}>\n                <Button variant=\"outline\" className=\"flex items-center space-x-2\">\n                  <ChevronLeft className=\"h-4 w-4\" />\n                  <span>Previous</span>\n                </Button>\n              </Link>\n            )}\n\n            {/* Page Numbers */}\n            <div className=\"flex items-center space-x-1\">\n              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (\n                <Link key={page} href={createPageUrl(page)}>\n                  <Button\n                    variant={currentPage === page ? \"default\" : \"outline\"}\n                    className={`w-10 h-10 ${\n                      currentPage === page \n                        ? 'bg-brand-navy hover:bg-brand-navy-dark' \n                        : 'hover:bg-brand-navy hover:text-white'\n                    }`}\n                  >\n                    {page}\n                  </Button>\n                </Link>\n              ))}\n            </div>\n\n            {/* Next Button */}\n            {currentPage < totalPages && (\n              <Link href={createPageUrl(currentPage + 1)}>\n                <Button variant=\"outline\" className=\"flex items-center space-x-2\">\n                  <span>Next</span>\n                  <ChevronRight className=\"h-4 w-4\" />\n                </Button>\n              </Link>\n            )}\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AA0Ce,SAAS,SAAS,KAA+D;QAA/D,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAiB,GAA/D;IAC/B,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,IAAI;QACnB,IAAI,aAAa,QAAQ,EAAE,OAAO,GAAG,CAAC,YAAY,aAAa,QAAQ;QACvE,IAAI,aAAa,GAAG,EAAE,OAAO,GAAG,CAAC,OAAO,aAAa,GAAG;QACxD,IAAI,aAAa,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,aAAa,MAAM;QACjE,IAAI,OAAO,GAAG,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ;QAC9C,OAAO,AAAC,QAAwD,OAAjD,OAAO,QAAQ,KAAK,AAAC,IAAqB,OAAlB,OAAO,QAAQ,MAAO;IAC/D;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQvE;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;;oCACV,MAAM,MAAM;oCAAC;oCAAS,MAAM,MAAM,KAAK,IAAI,MAAM;oCAAG;oCACpD,aAAa,QAAQ,IAAI,AAAC,OAA4B,OAAtB,aAAa,QAAQ;oCACrD,aAAa,MAAM,IAAI,AAAC,SAA4B,OAApB,aAAa,MAAM,EAAC;;;;;;;;;;;;;;;;;;8BAM3D,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,AAAC,SAAkB,OAAV,KAAK,IAAI;gCAAI,WAAU;0CAC1C,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,aAAa;oDACvB,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;kEAET,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;gDAGtB,KAAK,QAAQ,kBACZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4C;;;;;;;;;;;;;;;;;sDAOnE,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DAErB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;8EAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;sEAEpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;;wEAAM,KAAK,WAAW;wEAAC;;;;;;;;;;;;;;;;;;;8DAK5B,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAIb,6LAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;8DAIf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,KAAK,MAAM,CAAC,MAAM;oEACvB,KAAK,KAAK,MAAM,CAAC,IAAI;oEACrB,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFAA+B,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;sEAGlE,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtEzB,KAAK,EAAE;;;;;;;;;;gBAgFjB,aAAa,mBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;wBAGT,cAAc,mBACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,cAAc,cAAc;sCACtC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,qBACxD,6LAAC,+JAAA,CAAA,UAAI;oCAAY,MAAM,cAAc;8CACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,gBAAgB,OAAO,YAAY;wCAC5C,WAAW,AAAC,aAIX,OAHC,gBAAgB,OACZ,2CACA;kDAGL;;;;;;mCATM;;;;;;;;;;wBAgBd,cAAc,4BACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,cAAc,cAAc;sCACtC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;KAtMwB", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-categories.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { Filter, Grid, List } from 'lucide-react';\n\ninterface Category {\n  id: string;\n  name: string;\n  slug: string;\n  description: string;\n  color: string;\n  postCount: number;\n}\n\ninterface BlogCategoriesProps {\n  categories: Category[];\n  activeCategory?: string;\n}\n\nexport default function BlogCategories({ categories, activeCategory }: BlogCategoriesProps) {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n\n  const handleCategoryClick = (categorySlug: string) => {\n    const params = new URLSearchParams(searchParams);\n    if (categorySlug === 'all') {\n      params.delete('category');\n    } else {\n      params.set('category', categorySlug);\n    }\n    params.delete('page'); // Reset to first page\n    router.push(`/blog?${params.toString()}`);\n  };\n\n  return (\n    <section className=\"py-12 bg-white border-b border-gray-100\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\"\n        >\n          <div>\n            <h2 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-2\">\n              Browse by Category\n            </h2>\n            <p className=\"text-gray-600\">\n              Find articles that match your interests and business needs\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-2 mt-4 md:mt-0\">\n            <Filter className=\"h-5 w-5 text-gray-400\" />\n            <span className=\"text-sm text-gray-500\">Filter & Sort</span>\n          </div>\n        </motion.div>\n\n        {/* Categories Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-8\">\n          {/* All Categories */}\n          <motion.button\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            onClick={() => handleCategoryClick('all')}\n            className={`group p-6 rounded-2xl border-2 transition-all duration-300 text-left hover:shadow-lg hover:-translate-y-1 ${\n              !activeCategory\n                ? 'border-brand-navy bg-brand-navy text-white'\n                : 'border-gray-200 bg-white text-gray-900 hover:border-brand-navy/30'\n            }`}\n          >\n            <div className=\"flex items-center justify-between mb-3\">\n              <Grid className={`h-6 w-6 ${!activeCategory ? 'text-brand-gold' : 'text-brand-navy'}`} />\n              <span className={`text-sm font-medium px-2 py-1 rounded-full ${\n                !activeCategory \n                  ? 'bg-brand-gold/20 text-brand-gold' \n                  : 'bg-brand-navy/10 text-brand-navy'\n              }`}>\n                All\n              </span>\n            </div>\n            <h3 className=\"font-bold text-lg mb-2\">All Articles</h3>\n            <p className={`text-sm ${!activeCategory ? 'text-blue-100' : 'text-gray-600'}`}>\n              Browse all our expert insights and tips\n            </p>\n          </motion.button>\n\n          {/* Individual Categories */}\n          {categories.map((category, index) => (\n            <motion.button\n              key={category.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              onClick={() => handleCategoryClick(category.slug)}\n              className={`group p-6 rounded-2xl border-2 transition-all duration-300 text-left hover:shadow-lg hover:-translate-y-1 ${\n                activeCategory === category.slug\n                  ? 'border-brand-navy bg-brand-navy text-white'\n                  : 'border-gray-200 bg-white text-gray-900 hover:border-brand-navy/30'\n              }`}\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <div \n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: category.color }}\n                ></div>\n                <span className={`text-sm font-medium px-2 py-1 rounded-full ${\n                  activeCategory === category.slug \n                    ? 'bg-brand-gold/20 text-brand-gold' \n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {category.postCount}\n                </span>\n              </div>\n              <h3 className=\"font-bold text-lg mb-2\">{category.name}</h3>\n              <p className={`text-sm ${\n                activeCategory === category.slug ? 'text-blue-100' : 'text-gray-600'\n              }`}>\n                {category.description}\n              </p>\n            </motion.button>\n          ))}\n        </div>\n\n        {/* Active Filter Display */}\n        {activeCategory && (\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.4 }}\n            className=\"flex items-center space-x-3 p-4 bg-brand-navy/5 rounded-xl border border-brand-navy/20\"\n          >\n            <Filter className=\"h-5 w-5 text-brand-navy\" />\n            <span className=\"text-brand-navy font-medium\">\n              Showing articles in: \n            </span>\n            <span className=\"px-3 py-1 bg-brand-navy text-white rounded-full text-sm font-medium\">\n              {categories.find(cat => cat.slug === activeCategory)?.name}\n            </span>\n            <button\n              onClick={() => handleCategoryClick('all')}\n              className=\"text-brand-navy hover:text-brand-navy-dark text-sm font-medium underline\"\n            >\n              Clear filter\n            </button>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;;;AALA;;;;AAqBe,SAAS,eAAe,KAAmD;QAAnD,EAAE,UAAU,EAAE,cAAc,EAAuB,GAAnD;QA2HxB;;IA1Hb,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,sBAAsB,CAAC;QAC3B,MAAM,SAAS,IAAI,gBAAgB;QACnC,IAAI,iBAAiB,OAAO;YAC1B,OAAO,MAAM,CAAC;QAChB,OAAO;YACL,OAAO,GAAG,CAAC,YAAY;QACzB;QACA,OAAO,MAAM,CAAC,SAAS,sBAAsB;QAC7C,OAAO,IAAI,CAAC,AAAC,SAA0B,OAAlB,OAAO,QAAQ;IACtC;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAK5C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,SAAS,IAAM,oBAAoB;4BACnC,WAAW,AAAC,6GAIX,OAHC,CAAC,iBACG,+CACA;;8CAGN,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,4MAAA,CAAA,OAAI;4CAAC,WAAW,AAAC,WAAkE,OAAxD,CAAC,iBAAiB,oBAAoB;;;;;;sDAClE,6LAAC;4CAAK,WAAW,AAAC,8CAIjB,OAHC,CAAC,iBACG,qCACA;sDACF;;;;;;;;;;;;8CAIN,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAE,WAAW,AAAC,WAA8D,OAApD,CAAC,iBAAiB,kBAAkB;8CAAmB;;;;;;;;;;;;wBAMjF,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,SAAS,IAAM,oBAAoB,SAAS,IAAI;gCAChD,WAAW,AAAC,6GAIX,OAHC,mBAAmB,SAAS,IAAI,GAC5B,+CACA;;kDAGN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;;;;;;0DAE3C,6LAAC;gDAAK,WAAW,AAAC,8CAIjB,OAHC,mBAAmB,SAAS,IAAI,GAC5B,qCACA;0DAEH,SAAS,SAAS;;;;;;;;;;;;kDAGvB,6LAAC;wCAAG,WAAU;kDAA0B,SAAS,IAAI;;;;;;kDACrD,6LAAC;wCAAE,WAAW,AAAC,WAEd,OADC,mBAAmB,SAAS,IAAI,GAAG,kBAAkB;kDAEpD,SAAS,WAAW;;;;;;;+BA7BlB,SAAS,EAAE;;;;;;;;;;;gBAoCrB,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAK,WAAU;sCAA8B;;;;;;sCAG9C,6LAAC;4BAAK,WAAU;uCACb,mBAAA,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,6BAApC,uCAAA,iBAAqD,IAAI;;;;;;sCAE5D,6LAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAxIwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFd", "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/blog-newsletter.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { Mail, Send, CheckCircle, TrendingUp, Users, Award } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport default function BlogNewsletter() {\n  const [email, setEmail] = useState('');\n  const [isSubscribed, setIsSubscribed] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n\n    setIsLoading(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      setIsSubscribed(true);\n      setIsLoading(false);\n      setEmail('');\n    }, 1500);\n  };\n\n  const benefits = [\n    {\n      icon: TrendingUp,\n      title: 'Latest Trends',\n      description: 'Get the newest digital marketing trends delivered to your inbox'\n    },\n    {\n      icon: Users,\n      title: 'Expert Insights',\n      description: 'Learn from our team of digital marketing experts and industry leaders'\n    },\n    {\n      icon: Award,\n      title: 'Exclusive Content',\n      description: 'Access subscriber-only content, case studies, and actionable tips'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-brand-navy to-brand-navy-dark relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-white/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Header */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"mb-12\"\n          >\n            <div className=\"inline-flex items-center px-4 py-2 bg-brand-gold/20 text-brand-gold rounded-full text-sm font-medium mb-6\">\n              <Mail className=\"w-4 h-4 mr-2\" />\n              Newsletter Subscription\n            </div>\n            \n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6\">\n              Stay Ahead of the\n              <span className=\"block text-brand-gold\">Digital Marketing Game</span>\n            </h2>\n            \n            <p className=\"text-blue-100 text-lg md:text-xl leading-relaxed max-w-3xl mx-auto\">\n              Join 5,000+ business owners and marketers who receive our weekly newsletter \n              packed with actionable insights, case studies, and the latest trends in digital marketing.\n            </p>\n          </motion.div>\n\n          {/* Benefits */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\"\n          >\n            {benefits.map((benefit, index) => (\n              <motion.div\n                key={benefit.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center\"\n              >\n                <div className=\"w-16 h-16 bg-brand-gold/20 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <benefit.icon className=\"h-8 w-8 text-brand-gold\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-white mb-3\">{benefit.title}</h3>\n                <p className=\"text-blue-100 leading-relaxed\">{benefit.description}</p>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Newsletter Form */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"max-w-2xl mx-auto\"\n          >\n            {!isSubscribed ? (\n              <form onSubmit={handleSubmit} className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-2 border border-white/20\">\n                <div className=\"flex flex-col md:flex-row gap-2\">\n                  <input\n                    type=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    placeholder=\"Enter your email address...\"\n                    className=\"flex-1 px-6 py-4 bg-white/90 text-gray-900 placeholder-gray-500 rounded-xl border-none outline-none text-lg\"\n                    required\n                  />\n                  <Button\n                    type=\"submit\"\n                    disabled={isLoading}\n                    className=\"px-8 py-4 bg-brand-gold hover:bg-brand-gold/90 text-gray-900 font-semibold rounded-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isLoading ? (\n                      <div className=\"flex items-center space-x-2\">\n                        <div className=\"w-5 h-5 border-2 border-gray-900/30 border-t-gray-900 rounded-full animate-spin\"></div>\n                        <span>Subscribing...</span>\n                      </div>\n                    ) : (\n                      <div className=\"flex items-center space-x-2\">\n                        <Send className=\"h-5 w-5\" />\n                        <span>Subscribe Now</span>\n                      </div>\n                    )}\n                  </Button>\n                </div>\n              </form>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6 }}\n                className=\"bg-green-500/20 border border-green-500/30 rounded-2xl p-8 text-center\"\n              >\n                <CheckCircle className=\"h-16 w-16 text-green-400 mx-auto mb-4\" />\n                <h3 className=\"text-2xl font-bold text-white mb-2\">Successfully Subscribed!</h3>\n                <p className=\"text-green-100\">\n                  Thank you for subscribing! You'll receive our next newsletter with the latest \n                  digital marketing insights and tips.\n                </p>\n              </motion.div>\n            )}\n\n            {!isSubscribed && (\n              <p className=\"text-blue-100/80 text-sm mt-4\">\n                No spam, ever. Unsubscribe anytime with just one click. \n                We respect your privacy and will never share your email.\n              </p>\n            )}\n          </motion.div>\n\n          {/* Social Proof */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            viewport={{ once: true }}\n            className=\"mt-12 flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8 text-blue-100\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <Users className=\"h-5 w-5 text-brand-gold\" />\n              <span className=\"font-semibold\">5,000+ Subscribers</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-5 w-5 text-brand-gold\" />\n              <span className=\"font-semibold\">Weekly Insights</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Award className=\"h-5 w-5 text-brand-gold\" />\n              <span className=\"font-semibold\">Expert Content</span>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,OAAO;QAEZ,aAAa;QAEb,oBAAoB;QACpB,WAAW;YACT,gBAAgB;YAChB,aAAa;YACb,SAAS;QACX,GAAG;IACL;IAEA,MAAM,WAAW;QACf;YACE,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAInC,6LAAC;oCAAG,WAAU;;wCAA6D;sDAEzE,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAG1C,6LAAC;oCAAE,WAAU;8CAAqE;;;;;;;;;;;;sCAOpF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CAAG,WAAU;sDAAqC,QAAQ,KAAK;;;;;;sDAChE,6LAAC;4CAAE,WAAU;sDAAiC,QAAQ,WAAW;;;;;;;mCAX5D,QAAQ,KAAK;;;;;;;;;;sCAiBxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCAET,CAAC,6BACA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;8CACtC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;0DAEV,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,0BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;sEAAK;;;;;;;;;;;yEAGR,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAOhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAEV,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;gCAOjC,CAAC,8BACA,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAQjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;GAxLwB;KAAA", "debugId": null}}]}