{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/contact-hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { Phone, Mail, MapPin, Clock, MessageSquare } from 'lucide-react';\n\nconst contactMethods = [\n  {\n    icon: Phone,\n    title: 'Call Us',\n    value: '+977-1-4441234',\n    description: 'Mon-Fr<PERSON>, 9:00 AM - 6:00 PM',\n    href: 'tel:+97714441234'\n  },\n  {\n    icon: Mail,\n    title: 'Email Us',\n    value: '<EMAIL>',\n    description: 'We reply within 24 hours',\n    href: 'mailto:<EMAIL>'\n  },\n  {\n    icon: MessageSquare,\n    title: 'WhatsApp',\n    value: '+977-9841234567',\n    description: 'Quick chat anytime',\n    href: 'https://wa.me/9779841234567'\n  },\n  {\n    icon: MapPin,\n    title: 'Visit Us',\n    value: 'Thamel, Kathmandu',\n    description: 'Nepal',\n    href: 'https://maps.google.com/?q=Thamel,Kathmandu,Nepal'\n  }\n];\n\nexport default function ContactHero() {\n  return (\n    <section className=\"relative py-20 bg-gradient-to-br from-blue-50 via-white to-yellow-50 overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <Image\n          src=\"https://images.unsplash.com/photo-1423666639041-f56000c27a9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80\"\n          alt=\"Contact and communication background\"\n          fill\n          className=\"object-cover\"\n          priority\n        />\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 right-20 w-64 h-64 bg-brand-gold/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-brand-navy/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center mb-16\">\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"inline-flex items-center px-4 py-2 bg-brand-navy/10 text-brand-navy rounded-full text-sm font-medium mb-6\"\n          >\n            <MessageSquare className=\"w-4 h-4 mr-2 text-brand-gold\" />\n            Get In Touch\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight\"\n          >\n            Let&apos;s Start Your Digital\n            <span className=\"block bg-gradient-to-r from-brand-navy to-brand-gold bg-clip-text text-transparent\">\n              Success Story\n            </span>\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Ready to transform your business with digital marketing? We&apos;re here to help you \n            every step of the way. Get in touch for a free consultation and discover how we can \n            help your business thrive online.\n          </motion.p>\n\n          {/* Quick Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\"\n          >\n            {[\n              { value: '24hrs', label: 'Response Time' },\n              { value: '150+', label: 'Happy Clients' },\n              { value: '5+', label: 'Years Experience' },\n              { value: '98%', label: 'Satisfaction Rate' }\n            ].map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"text-2xl md:text-3xl font-bold text-brand-navy mb-2\">\n                  {stat.value}\n                </div>\n                <div className=\"text-gray-600 text-sm\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n\n        {/* Contact Methods Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {contactMethods.map((method, index) => (\n            <motion.a\n              key={method.title}\n              href={method.href}\n              target={method.href.startsWith('http') ? '_blank' : undefined}\n              rel={method.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100 h-full\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-gold rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <method.icon className=\"h-6 w-6 text-white\" />\n                </div>\n                \n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-brand-navy transition-colors\">\n                  {method.title}\n                </h3>\n                \n                <p className=\"text-brand-navy font-medium mb-1\">\n                  {method.value}\n                </p>\n                \n                <p className=\"text-gray-600 text-sm\">\n                  {method.description}\n                </p>\n              </div>\n            </motion.a>\n          ))}\n        </div>\n\n        {/* Office Hours */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 1.2 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-white rounded-3xl p-8 shadow-xl border border-gray-100 max-w-2xl mx-auto\">\n            <div className=\"flex items-center justify-center mb-4\">\n              <Clock className=\"h-8 w-8 text-brand-gold mr-3\" />\n              <h3 className=\"text-xl font-bold text-gray-900\">Office Hours</h3>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-600\">\n              <div>\n                <div className=\"font-semibold text-gray-900\">Monday - Friday</div>\n                <div>9:00 AM - 6:00 PM</div>\n              </div>\n              <div>\n                <div className=\"font-semibold text-gray-900\">Saturday</div>\n                <div>10:00 AM - 4:00 PM</div>\n              </div>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500\">\n              Sunday: Closed • Emergency support available 24/7\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,iBAAiB;IACrB;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,MAAM,6MAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;oBACV,QAAQ;;;;;;;;;;;0BAKZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAK5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAqF;;;;;;;;;;;;0CAMvG,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CACX;;;;;;0CAOD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET;oCACC;wCAAE,OAAO;wCAAS,OAAO;oCAAgB;oCACzC;wCAAE,OAAO;wCAAQ,OAAO;oCAAgB;oCACxC;wCAAE,OAAO;wCAAM,OAAO;oCAAmB;oCACzC;wCAAE,OAAO;wCAAO,OAAO;oCAAoB;iCAC5C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,KAAK;;;;;;;uCAT7C,KAAK,KAAK;;;;;;;;;;;;;;;;kCAgBvB,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,MAAM,OAAO,IAAI;gCACjB,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;gCACpD,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;gCAC9D,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAGzB,6LAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDACV,OAAO,KAAK;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDACV,OAAO,WAAW;;;;;;;;;;;;+BAvBlB,OAAO,KAAK;;;;;;;;;;kCA+BvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;;8CAElD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,6LAAC;8DAAI;;;;;;;;;;;;sDAEP,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;8CAGT,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD;KAvJwB", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,6JAAA,CAAA,gBAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,6JAAA,CAAA,aAAgB,CAAC;IACtC,MAAM,cAAc,6JAAA,CAAA,aAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,6JAAA,CAAA,gBAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,6JAAA,CAAA,QAAW;IAEtB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,AAAC,GAAoB,OAAlB,qBACH,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd;QAE9B,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/contact-form.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Send, CheckCircle, User, Mail, Phone, Building, MessageSquare, DollarSign, Calendar } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\n\nconst contactFormSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phone: z.string().optional(),\n  company: z.string().optional(),\n  service: z.string().min(1, 'Please select a service'),\n  budget: z.string().min(1, 'Please select your budget range'),\n  timeline: z.string().min(1, 'Please select your timeline'),\n  message: z.string().min(10, 'Message must be at least 10 characters'),\n});\n\ntype ContactFormData = z.infer<typeof contactFormSchema>;\n\nconst services = [\n  'Social Media Marketing',\n  'Search Engine Optimization (SEO)',\n  'Web Development & Design',\n  'Google Ads Management',\n  'Content Marketing',\n  'Brand Identity & Design',\n  'Complete Digital Marketing Package',\n  'Custom Solution'\n];\n\nconst budgetRanges = [\n  'Under NPR 50,000',\n  'NPR 50,000 - 100,000',\n  'NPR 100,000 - 200,000',\n  'NPR 200,000 - 500,000',\n  'NPR 500,000+',\n  'Let\\'s discuss'\n];\n\nconst timelines = [\n  'ASAP (Within 1 month)',\n  '1-3 months',\n  '3-6 months',\n  '6+ months',\n  'Just exploring options'\n];\n\nexport default function ContactForm() {\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const form = useForm<ContactFormData>({\n    resolver: zodResolver(contactFormSchema),\n    defaultValues: {\n      name: '',\n      email: '',\n      phone: '',\n      company: '',\n      service: '',\n      budget: '',\n      timeline: '',\n      message: '',\n    },\n  });\n\n  const onSubmit = async (data: ContactFormData) => {\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    console.log('Contact form submitted:', data);\n    setIsSubmitted(true);\n    setIsSubmitting(false);\n    form.reset();\n  };\n\n  if (isSubmitted) {\n    return (\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n            className=\"max-w-2xl mx-auto text-center\"\n          >\n            <div className=\"w-20 h-20 bg-brand-gold rounded-full flex items-center justify-center mx-auto mb-6\">\n              <CheckCircle className=\"h-10 w-10 text-gray-900\" />\n            </div>\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Thank You for Reaching Out!\n            </h2>\n            <p className=\"text-gray-600 mb-8 text-lg\">\n              We&apos;ve received your message and will get back to you within 24 hours with a \n              detailed proposal tailored to your needs. Our team is excited to help your business grow!\n            </p>\n            <div className=\"bg-gray-50 rounded-2xl p-6 mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">What happens next?</h3>\n              <div className=\"space-y-3 text-left\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-6 h-6 bg-brand-navy text-white rounded-full flex items-center justify-center text-sm font-bold\">1</div>\n                  <span className=\"text-gray-700\">Our team reviews your requirements</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-6 h-6 bg-brand-navy text-white rounded-full flex items-center justify-center text-sm font-bold\">2</div>\n                  <span className=\"text-gray-700\">We prepare a customized strategy proposal</span>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-6 h-6 bg-brand-navy text-white rounded-full flex items-center justify-center text-sm font-bold\">3</div>\n                  <span className=\"text-gray-700\">We schedule a consultation call with you</span>\n                </div>\n              </div>\n            </div>\n            <Button \n              onClick={() => setIsSubmitted(false)}\n              className=\"bg-brand-navy hover:bg-brand-navy-dark text-white font-semibold\"\n            >\n              Send Another Message\n            </Button>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <div className=\"sticky top-8\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Tell Us About Your\n                <span className=\"block text-brand-navy\">Project</span>\n              </h2>\n              \n              <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n                The more details you provide, the better we can tailor our proposal to your \n                specific needs. Don&apos;t worry if you&apos;re not sure about everything – \n                we&apos;ll help you figure it out during our consultation.\n              </p>\n\n              <div className=\"space-y-6\">\n                {[\n                  {\n                    icon: CheckCircle,\n                    title: 'Free Consultation',\n                    description: 'No cost, no obligation 30-minute strategy session'\n                  },\n                  {\n                    icon: CheckCircle,\n                    title: 'Custom Proposal',\n                    description: 'Tailored strategy and pricing based on your goals'\n                  },\n                  {\n                    icon: CheckCircle,\n                    title: 'Expert Guidance',\n                    description: 'Professional advice from Nepal market specialists'\n                  }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={benefit.title}\n                    initial={{ opacity: 0, x: -20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"flex items-start space-x-4\"\n                  >\n                    <benefit.icon className=\"h-6 w-6 text-brand-gold mt-1 flex-shrink-0\" />\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-1\">{benefit.title}</h4>\n                      <p className=\"text-gray-600 text-sm\">{benefit.description}</p>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Right Content - Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"shadow-2xl border-0\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-gray-900 flex items-center\">\n                  <MessageSquare className=\"h-6 w-6 mr-3 text-brand-gold\" />\n                  Get Your Free Consultation\n                </CardTitle>\n                <CardDescription>\n                  Fill out the form below and we&apos;ll get back to you within 24 hours with a \n                  detailed proposal for your digital marketing needs.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Form {...form}>\n                  <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n                    {/* Personal Information */}\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <FormField\n                        control={form.control}\n                        name=\"name\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel className=\"flex items-center\">\n                              <User className=\"h-4 w-4 mr-2\" />\n                              Full Name *\n                            </FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"Your full name\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                      \n                      <FormField\n                        control={form.control}\n                        name=\"email\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel className=\"flex items-center\">\n                              <Mail className=\"h-4 w-4 mr-2\" />\n                              Email Address *\n                            </FormLabel>\n                            <FormControl>\n                              <Input type=\"email\" placeholder=\"<EMAIL>\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <FormField\n                        control={form.control}\n                        name=\"phone\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel className=\"flex items-center\">\n                              <Phone className=\"h-4 w-4 mr-2\" />\n                              Phone Number\n                            </FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"+977-98xxxxxxxx\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                      \n                      <FormField\n                        control={form.control}\n                        name=\"company\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel className=\"flex items-center\">\n                              <Building className=\"h-4 w-4 mr-2\" />\n                              Company Name\n                            </FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"Your company name\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                    </div>\n\n                    {/* Project Details */}\n                    <FormField\n                      control={form.control}\n                      name=\"service\"\n                      render={({ field }) => (\n                        <FormItem>\n                          <FormLabel>Service of Interest *</FormLabel>\n                          <Select onValueChange={field.onChange} defaultValue={field.value}>\n                            <FormControl>\n                              <SelectTrigger>\n                                <SelectValue placeholder=\"Select a service\" />\n                              </SelectTrigger>\n                            </FormControl>\n                            <SelectContent>\n                              {services.map((service) => (\n                                <SelectItem key={service} value={service}>\n                                  {service}\n                                </SelectItem>\n                              ))}\n                            </SelectContent>\n                          </Select>\n                          <FormMessage />\n                        </FormItem>\n                      )}\n                    />\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <FormField\n                        control={form.control}\n                        name=\"budget\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel className=\"flex items-center\">\n                              <DollarSign className=\"h-4 w-4 mr-2\" />\n                              Budget Range *\n                            </FormLabel>\n                            <Select onValueChange={field.onChange} defaultValue={field.value}>\n                              <FormControl>\n                                <SelectTrigger>\n                                  <SelectValue placeholder=\"Select budget range\" />\n                                </SelectTrigger>\n                              </FormControl>\n                              <SelectContent>\n                                {budgetRanges.map((budget) => (\n                                  <SelectItem key={budget} value={budget}>\n                                    {budget}\n                                  </SelectItem>\n                                ))}\n                              </SelectContent>\n                            </Select>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n\n                      <FormField\n                        control={form.control}\n                        name=\"timeline\"\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel className=\"flex items-center\">\n                              <Calendar className=\"h-4 w-4 mr-2\" />\n                              Timeline *\n                            </FormLabel>\n                            <Select onValueChange={field.onChange} defaultValue={field.value}>\n                              <FormControl>\n                                <SelectTrigger>\n                                  <SelectValue placeholder=\"Select timeline\" />\n                                </SelectTrigger>\n                              </FormControl>\n                              <SelectContent>\n                                {timelines.map((timeline) => (\n                                  <SelectItem key={timeline} value={timeline}>\n                                    {timeline}\n                                  </SelectItem>\n                                ))}\n                              </SelectContent>\n                            </Select>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                    </div>\n\n                    <FormField\n                      control={form.control}\n                      name=\"message\"\n                      render={({ field }) => (\n                        <FormItem>\n                          <FormLabel>Tell us about your project *</FormLabel>\n                          <FormControl>\n                            <Textarea \n                              placeholder=\"Describe your business goals, current challenges, target audience, and what you hope to achieve with digital marketing...\"\n                              className=\"min-h-[120px]\"\n                              {...field}\n                            />\n                          </FormControl>\n                          <FormMessage />\n                        </FormItem>\n                      )}\n                    />\n\n                    <Button \n                      type=\"submit\" \n                      className=\"w-full bg-brand-navy hover:bg-brand-navy-dark text-white py-3 text-lg font-semibold\"\n                      disabled={isSubmitting}\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                          Sending Message...\n                        </>\n                      ) : (\n                        <>\n                          Send Message\n                          <Send className=\"ml-2 h-5 w-5\" />\n                        </>\n                      )}\n                    </Button>\n\n                    <p className=\"text-xs text-gray-500 text-center\">\n                      By submitting this form, you agree to receive marketing communications from Lunar Cubes. \n                      You can unsubscribe at any time. We respect your privacy.\n                    </p>\n                  </form>\n                </Form>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAeA,MAAM,oBAAoB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;AAC9B;AAIA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,SAAS;YACT,QAAQ;YACR,UAAU;YACV,SAAS;QACX;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,QAAQ,GAAG,CAAC,2BAA2B;QACvC,eAAe;QACf,gBAAgB;QAChB,KAAK,KAAK;IACZ;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmG;;;;;;8DAClH,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmG;;;;;;8DAClH,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmG;;;;;;8DAClH,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAItC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAoD;sDAEhE,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAG1C,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAM1D,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,MAAM,8NAAA,CAAA,cAAW;4CACjB,OAAO;4CACP,aAAa;wCACf;wCACA;4CACE,MAAM,8NAAA,CAAA,cAAW;4CACjB,OAAO;4CACP,aAAa;wCACf;wCACA;4CACE,MAAM,8NAAA,CAAA,cAAW;4CACjB,OAAO;4CACP,aAAa;wCACf;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;8DACxB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAoC,QAAQ,KAAK;;;;;;sEAC/D,6LAAC;4DAAE,WAAU;sEAAyB,QAAQ,WAAW;;;;;;;;;;;;;2CAVtD,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;kCAmB5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAG5D,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAKnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAE,GAAG,IAAI;kDACZ,cAAA,6LAAC;4CAAK,UAAU,KAAK,YAAY,CAAC;4CAAW,WAAU;;8DAErD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ;oEAAC,EAAE,KAAK,EAAE;qFAChB,6LAAC,mIAAA,CAAA,WAAQ;;sFACP,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,6LAAC,mIAAA,CAAA,cAAW;sFACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gFAAC,aAAY;gFAAkB,GAAG,KAAK;;;;;;;;;;;sFAE/C,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;sEAKlB,6LAAC,mIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ;oEAAC,EAAE,KAAK,EAAE;qFAChB,6LAAC,mIAAA,CAAA,WAAQ;;sFACP,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,6LAAC,mIAAA,CAAA,cAAW;sFACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gFAAC,MAAK;gFAAQ,aAAY;gFAAkB,GAAG,KAAK;;;;;;;;;;;sFAE5D,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ;oEAAC,EAAE,KAAK,EAAE;qFAChB,6LAAC,mIAAA,CAAA,WAAQ;;sFACP,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGpC,6LAAC,mIAAA,CAAA,cAAW;sFACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gFAAC,aAAY;gFAAmB,GAAG,KAAK;;;;;;;;;;;sFAEhD,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;sEAKlB,6LAAC,mIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ;oEAAC,EAAE,KAAK,EAAE;qFAChB,6LAAC,mIAAA,CAAA,WAAQ;;sFACP,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGvC,6LAAC,mIAAA,CAAA,cAAW;sFACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gFAAC,aAAY;gFAAqB,GAAG,KAAK;;;;;;;;;;;sFAElD,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;8DAOpB,6LAAC,mIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ;4DAAC,EAAE,KAAK,EAAE;6EAChB,6LAAC,mIAAA,CAAA,WAAQ;;8EACP,6LAAC,mIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,qIAAA,CAAA,SAAM;oEAAC,eAAe,MAAM,QAAQ;oEAAE,cAAc,MAAM,KAAK;;sFAC9D,6LAAC,mIAAA,CAAA,cAAW;sFACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;0FACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,6LAAC,qIAAA,CAAA,gBAAa;sFACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,qIAAA,CAAA,aAAU;oFAAe,OAAO;8FAC9B;mFADc;;;;;;;;;;;;;;;;8EAMvB,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;8DAKlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ;oEAAC,EAAE,KAAK,EAAE;qFAChB,6LAAC,mIAAA,CAAA,WAAQ;;sFACP,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,qNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGzC,6LAAC,qIAAA,CAAA,SAAM;4EAAC,eAAe,MAAM,QAAQ;4EAAE,cAAc,MAAM,KAAK;;8FAC9D,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;kGACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4FAAC,aAAY;;;;;;;;;;;;;;;;8FAG7B,6LAAC,qIAAA,CAAA,gBAAa;8FACX,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,aAAU;4FAAc,OAAO;sGAC7B;2FADc;;;;;;;;;;;;;;;;sFAMvB,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;sEAKlB,6LAAC,mIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAK;4DACL,QAAQ;oEAAC,EAAE,KAAK,EAAE;qFAChB,6LAAC,mIAAA,CAAA,WAAQ;;sFACP,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;8FACnB,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGvC,6LAAC,qIAAA,CAAA,SAAM;4EAAC,eAAe,MAAM,QAAQ;4EAAE,cAAc,MAAM,KAAK;;8FAC9D,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;kGACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4FAAC,aAAY;;;;;;;;;;;;;;;;8FAG7B,6LAAC,qIAAA,CAAA,gBAAa;8FACX,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;4FAAgB,OAAO;sGAC/B;2FADc;;;;;;;;;;;;;;;;sFAMvB,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,6LAAC,mIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ;4DAAC,EAAE,KAAK,EAAE;6EAChB,6LAAC,mIAAA,CAAA,WAAQ;;8EACP,6LAAC,mIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,mIAAA,CAAA,cAAW;8EACV,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wEACP,aAAY;wEACZ,WAAU;wEACT,GAAG,KAAK;;;;;;;;;;;8EAGb,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;8DAKlB,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,UAAU;8DAET,6BACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;4DAAE;0EAEA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;8DAKtB,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarE;GA9WwB;;QAIT,iKAAA,CAAA,UAAO;;;KAJE", "debugId": null}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/lunar/src/components/sections/contact-info.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { MapPin, Phone, Mail, Clock, Facebook, Instagram, Linkedin, Twitter, MessageSquare } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { useCompanyInfo } from '@/lib/queries/hooks';\n\nexport default function ContactInfo() {\n  const { data: company } = useCompanyInfo();\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n            Visit Our Office or\n            <span className=\"block text-brand-navy\">Connect Online</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            We&apos;re located in the heart of Kathmandu and always ready to meet in person \n            or connect virtually. Choose what works best for you.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            {/* Office Location */}\n            <Card className=\"border-0 shadow-lg\">\n              <CardContent className=\"p-8\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-brand-navy to-brand-gold rounded-xl flex items-center justify-center flex-shrink-0\">\n                    <MapPin className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Our Office</h3>\n                    {company && (\n                      <>\n                        <p className=\"text-gray-700 mb-2\">\n                          {company.location.address}\n                        </p>\n                        <p className=\"text-gray-700 mb-4\">\n                          {company.location.city}, {company.location.country}\n                        </p>\n                      </>\n                    )}\n                    <Button \n                      asChild \n                      variant=\"outline\" \n                      size=\"sm\"\n                      className=\"border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white\"\n                    >\n                      <a \n                        href=\"https://maps.google.com/?q=Thamel,Kathmandu,Nepal\" \n                        target=\"_blank\" \n                        rel=\"noopener noreferrer\"\n                      >\n                        Get Directions\n                      </a>\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Contact Methods */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Phone */}\n              <Card className=\"border-0 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-10 h-10 bg-brand-navy rounded-lg flex items-center justify-center\">\n                      <Phone className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">Call Us</h4>\n                      {company && (\n                        <a \n                          href={`tel:${company.contact.phone}`}\n                          className=\"text-brand-navy hover:underline\"\n                        >\n                          {company.contact.phone}\n                        </a>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Email */}\n              <Card className=\"border-0 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-10 h-10 bg-brand-gold rounded-lg flex items-center justify-center\">\n                      <Mail className=\"h-5 w-5 text-gray-900\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">Email Us</h4>\n                      {company && (\n                        <a \n                          href={`mailto:${company.contact.email}`}\n                          className=\"text-brand-navy hover:underline\"\n                        >\n                          {company.contact.email}\n                        </a>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* WhatsApp */}\n              <Card className=\"border-0 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center\">\n                      <MessageSquare className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">WhatsApp</h4>\n                      {company && (\n                        <a \n                          href={`https://wa.me/${company.contact.whatsapp?.replace(/[^0-9]/g, '')}`}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-brand-navy hover:underline\"\n                        >\n                          {company.contact.whatsapp}\n                        </a>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Office Hours */}\n              <Card className=\"border-0 shadow-lg hover:shadow-xl transition-shadow duration-300\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center\">\n                      <Clock className=\"h-5 w-5 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">Office Hours</h4>\n                      <div className=\"text-sm text-gray-600\">\n                        <div>Mon-Fri: 9AM-6PM</div>\n                        <div>Sat: 10AM-4PM</div>\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Social Media */}\n            <Card className=\"border-0 shadow-lg\">\n              <CardContent className=\"p-8\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Follow Us</h3>\n                <div className=\"flex space-x-4\">\n                  {company?.social && (\n                    <>\n                      {company.social.facebook && (\n                        <a\n                          href={company.social.facebook}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors\"\n                        >\n                          <Facebook className=\"h-5 w-5\" />\n                        </a>\n                      )}\n                      {company.social.instagram && (\n                        <a\n                          href={company.social.instagram}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center text-white hover:bg-pink-700 transition-colors\"\n                        >\n                          <Instagram className=\"h-5 w-5\" />\n                        </a>\n                      )}\n                      {company.social.linkedin && (\n                        <a\n                          href={company.social.linkedin}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-12 h-12 bg-blue-700 rounded-lg flex items-center justify-center text-white hover:bg-blue-800 transition-colors\"\n                        >\n                          <Linkedin className=\"h-5 w-5\" />\n                        </a>\n                      )}\n                      {company.social.twitter && (\n                        <a\n                          href={company.social.twitter}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors\"\n                        >\n                          <Twitter className=\"h-5 w-5\" />\n                        </a>\n                      )}\n                    </>\n                  )}\n                </div>\n                <p className=\"text-gray-600 text-sm mt-4\">\n                  Stay updated with our latest digital marketing tips, case studies, and industry insights.\n                </p>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Google Maps */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"border-0 shadow-lg overflow-hidden h-full\">\n              <CardContent className=\"p-0 h-full min-h-[600px]\">\n                <div className=\"relative w-full h-full\">\n                  {/* Google Maps Embed */}\n                  <iframe\n                    src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3532.1234567890123!2d85.3240!3d27.7172!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39eb196a8b0b0b0b%3A0x1234567890abcdef!2sThamel%2C%20Kathmandu%2C%20Nepal!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp\"\n                    width=\"100%\"\n                    height=\"100%\"\n                    style={{ border: 0, minHeight: '600px' }}\n                    allowFullScreen\n                    loading=\"lazy\"\n                    referrerPolicy=\"no-referrer-when-downgrade\"\n                    title=\"Lunar Cubes Office Location\"\n                    className=\"rounded-lg\"\n                  ></iframe>\n                  \n                  {/* Overlay with office info */}\n                  <div className=\"absolute top-4 left-4 bg-white rounded-lg p-4 shadow-lg max-w-xs\">\n                    <h4 className=\"font-bold text-gray-900 mb-2\">Lunar Cubes Office</h4>\n                    <p className=\"text-sm text-gray-600 mb-2\">\n                      Thamel, Kathmandu, Nepal\n                    </p>\n                    <div className=\"flex items-center text-xs text-gray-500\">\n                      <Clock className=\"h-3 w-3 mr-1\" />\n                      Open Mon-Fri 9AM-6PM\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n\n        {/* Emergency Contact */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-brand-navy to-brand-navy-dark rounded-3xl p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-4\">Need Urgent Support?</h3>\n            <p className=\"text-blue-100 mb-6\">\n              For existing clients with urgent issues, we provide 24/7 emergency support.\n            </p>\n            <Button \n              asChild \n              size=\"lg\" \n              className=\"bg-brand-gold hover:bg-brand-gold-dark text-gray-900 font-semibold\"\n            >\n              <a href=\"tel:+97714441234\">\n                Call Emergency Line\n              </a>\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;QAgIyB;;IA/H/C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEvC,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAE1C,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuC;;;;;;wDACpD,yBACC;;8EACE,6LAAC;oEAAE,WAAU;8EACV,QAAQ,QAAQ,CAAC,OAAO;;;;;;8EAE3B,6LAAC;oEAAE,WAAU;;wEACV,QAAQ,QAAQ,CAAC,IAAI;wEAAC;wEAAG,QAAQ,QAAQ,CAAC,OAAO;;;;;;;;;sEAIxD,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,6LAAC;gEACC,MAAK;gEACL,QAAO;gEACP,KAAI;0EACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUX,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;gEAC3C,yBACC,6LAAC;oEACC,MAAM,AAAC,OAA4B,OAAtB,QAAQ,OAAO,CAAC,KAAK;oEAClC,WAAU;8EAET,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASlC,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;gEAC3C,yBACC,6LAAC;oEACC,MAAM,AAAC,UAA+B,OAAtB,QAAQ,OAAO,CAAC,KAAK;oEACrC,WAAU;8EAET,QAAQ,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASlC,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;sEAE3B,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;gEAC3C,yBACC,6LAAC;oEACC,MAAM,AAAC,iBAAiE,QAAjD,4BAAA,QAAQ,OAAO,CAAC,QAAQ,cAAxB,gDAAA,0BAA0B,OAAO,CAAC,WAAW;oEACpE,QAAO;oEACP,KAAI;oEACJ,WAAU;8EAET,QAAQ,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASrC,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA8B;;;;;;8EAC5C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAI;;;;;;sFACL,6LAAC;sFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASjB,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ,CAAA,oBAAA,8BAAA,QAAS,MAAM,mBACd;;wDACG,QAAQ,MAAM,CAAC,QAAQ,kBACtB,6LAAC;4DACC,MAAM,QAAQ,MAAM,CAAC,QAAQ;4DAC7B,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;wDAGvB,QAAQ,MAAM,CAAC,SAAS,kBACvB,6LAAC;4DACC,MAAM,QAAQ,MAAM,CAAC,SAAS;4DAC9B,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,6LAAC,+MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;wDAGxB,QAAQ,MAAM,CAAC,QAAQ,kBACtB,6LAAC;4DACC,MAAM,QAAQ,MAAM,CAAC,QAAQ;4DAC7B,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;wDAGvB,QAAQ,MAAM,CAAC,OAAO,kBACrB,6LAAC;4DACC,MAAM,QAAQ,MAAM,CAAC,OAAO;4DAC5B,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAM7B,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;sCAQhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDACC,KAAI;gDACJ,OAAM;gDACN,QAAO;gDACP,OAAO;oDAAE,QAAQ;oDAAG,WAAW;gDAAQ;gDACvC,eAAe;gDACf,SAAQ;gDACR,gBAAe;gDACf,OAAM;gDACN,WAAU;;;;;;0DAIZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCAAE,MAAK;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GA5RwB;;QACI,iIAAA,CAAA,iBAAc;;;KADlB", "debugId": null}}]}